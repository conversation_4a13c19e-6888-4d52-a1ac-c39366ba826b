{"extends": ["next/core-web-vitals"], "rules": {"no-console": "off", "no-unused-vars": "off", "react-hooks/exhaustive-deps": "warn", "@typescript-eslint/no-unused-vars": "off", "@next/next/no-console": "off"}, "env": {"browser": true, "node": true, "es6": true}, "overrides": [{"files": ["**/*.ts", "**/*.tsx"], "rules": {"no-console": "off", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "off"}}]}