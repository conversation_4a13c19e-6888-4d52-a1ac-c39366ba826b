[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\dashboard\\stats\\route.ts": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\summary\\route.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\[id]\\route.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\[id]\\route.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\debts\\page.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\layout.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\page.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\products\\page.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\CustomerDebtSummary.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\DebtCard.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\DebtForm.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\Navigation.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\ProductCard.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\ProductForm.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\models\\CustomerDebt.ts": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\models\\Product.ts": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\mongodb.ts": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\types.ts": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\admin\\page.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\analytics\\page.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\admin\\seed\\route.ts": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\analytics\\route.ts": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\inventory\\insights\\route.ts": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\notifications\\route.ts": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\notifications\\page.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\NotificationCenter.tsx": "28"}, {"size": 3219, "mtime": 1750602727908, "results": "29", "hashOfConfig": "30"}, {"size": 2865, "mtime": 1750602709015, "results": "31", "hashOfConfig": "30"}, {"size": 3095, "mtime": 1750602754833, "results": "32", "hashOfConfig": "30"}, {"size": 3902, "mtime": 1750591102920, "results": "33", "hashOfConfig": "30"}, {"size": 2704, "mtime": 1750602688986, "results": "34", "hashOfConfig": "30"}, {"size": 4026, "mtime": 1750591103175, "results": "35", "hashOfConfig": "30"}, {"size": 14288, "mtime": 1750683742315, "results": "36", "hashOfConfig": "30"}, {"size": 618, "mtime": 1750591135161, "results": "37", "hashOfConfig": "30"}, {"size": 14774, "mtime": 1750670769458, "results": "38", "hashOfConfig": "30"}, {"size": 8195, "mtime": 1750683790613, "results": "39", "hashOfConfig": "30"}, {"size": 7998, "mtime": 1750603985508, "results": "40", "hashOfConfig": "30"}, {"size": 5413, "mtime": 1750603893162, "results": "41", "hashOfConfig": "30"}, {"size": 11200, "mtime": 1750603960281, "results": "42", "hashOfConfig": "30"}, {"size": 2480, "mtime": 1750670608248, "results": "43", "hashOfConfig": "30"}, {"size": 3617, "mtime": 1750603795936, "results": "44", "hashOfConfig": "30"}, {"size": 9655, "mtime": 1750603936356, "results": "45", "hashOfConfig": "30"}, {"size": 3520, "mtime": 1750604206697, "results": "46", "hashOfConfig": "30"}, {"size": 2257, "mtime": 1750604227433, "results": "47", "hashOfConfig": "30"}, {"size": 2090, "mtime": 1750604824421, "results": "48", "hashOfConfig": "30"}, {"size": 1623, "mtime": 1750603999427, "results": "49", "hashOfConfig": "30"}, {"size": 9640, "mtime": 1750670284290, "results": "50", "hashOfConfig": "30"}, {"size": 13948, "mtime": 1750683680640, "results": "51", "hashOfConfig": "30"}, {"size": 8259, "mtime": 1750682899137, "results": "52", "hashOfConfig": "30"}, {"size": 7805, "mtime": 1750670401273, "results": "53", "hashOfConfig": "30"}, {"size": 8349, "mtime": 1750683554050, "results": "54", "hashOfConfig": "30"}, {"size": 9042, "mtime": 1750683566004, "results": "55", "hashOfConfig": "30"}, {"size": 12855, "mtime": 1750683593678, "results": "56", "hashOfConfig": "30"}, {"size": 9676, "mtime": 1750683614089, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "3jp6v3", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\dashboard\\stats\\route.ts", ["142"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\route.ts", ["143", "144"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\summary\\route.ts", ["145"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\[id]\\route.ts", ["146", "147", "148"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\route.ts", ["149", "150"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\[id]\\route.ts", ["151", "152", "153"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\debts\\page.tsx", ["154", "155", "156", "157"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\page.tsx", ["158"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\products\\page.tsx", ["159", "160"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\CustomerDebtSummary.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\DebtCard.tsx", ["161", "162", "163"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\DebtForm.tsx", ["164"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\ProductCard.tsx", ["165", "166"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\ProductForm.tsx", ["167"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\models\\CustomerDebt.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\models\\Product.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\mongodb.ts", ["168", "169", "170", "171", "172", "173", "174"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\types.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\analytics\\page.tsx", ["175"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\admin\\seed\\route.ts", ["176", "177"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\analytics\\route.ts", ["178"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\inventory\\insights\\route.ts", ["179", "180"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\notifications\\route.ts", ["181", "182"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\notifications\\page.tsx", ["183"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\NotificationCenter.tsx", ["184"], [], {"ruleId": "185", "severity": 1, "message": "186", "line": 124, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 124, "endColumn": 18, "suggestions": "189"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 48, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 48, "endColumn": 18, "suggestions": "190"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 107, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 107, "endColumn": 18, "suggestions": "191"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 106, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 106, "endColumn": 18, "suggestions": "192"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 35, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 35, "endColumn": 18, "suggestions": "193"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 119, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 119, "endColumn": 18, "suggestions": "194"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 156, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 156, "endColumn": 18, "suggestions": "195"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 48, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 48, "endColumn": 18, "suggestions": "196"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 102, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 102, "endColumn": 18, "suggestions": "197"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 35, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 35, "endColumn": 18, "suggestions": "198"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 121, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 121, "endColumn": 18, "suggestions": "199"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 158, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 158, "endColumn": 18, "suggestions": "200"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 58, "column": 7, "nodeType": "187", "messageId": "188", "endLine": 58, "endColumn": 20, "suggestions": "201"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 74, "column": 7, "nodeType": "187", "messageId": "188", "endLine": 74, "endColumn": 20, "suggestions": "202"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 114, "column": 7, "nodeType": "187", "messageId": "188", "endLine": 114, "endColumn": 20, "suggestions": "203"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 142, "column": 7, "nodeType": "187", "messageId": "188", "endLine": 142, "endColumn": 20, "suggestions": "204"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 69, "column": 7, "nodeType": "187", "messageId": "188", "endLine": 69, "endColumn": 20, "suggestions": "205"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 38, "column": 7, "nodeType": "187", "messageId": "188", "endLine": 38, "endColumn": 20, "suggestions": "206"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 72, "column": 7, "nodeType": "187", "messageId": "188", "endLine": 72, "endColumn": 20, "suggestions": "207"}, {"ruleId": "208", "severity": 1, "message": "209", "line": 17, "column": 12, "nodeType": "210", "messageId": "211", "endLine": 17, "endColumn": 30}, {"ruleId": "208", "severity": 1, "message": "212", "line": 18, "column": 14, "nodeType": "210", "messageId": "211", "endLine": 18, "endColumn": 28}, {"ruleId": "208", "severity": 1, "message": "209", "line": 19, "column": 21, "nodeType": "210", "messageId": "211", "endLine": 19, "endColumn": 39}, {"ruleId": "185", "severity": 1, "message": "186", "line": 111, "column": 7, "nodeType": "187", "messageId": "188", "endLine": 111, "endColumn": 20, "suggestions": "213"}, {"ruleId": "208", "severity": 1, "message": "214", "line": 10, "column": 12, "nodeType": "210", "messageId": "211", "endLine": 10, "endColumn": 28}, {"ruleId": "208", "severity": 1, "message": "215", "line": 11, "column": 14, "nodeType": "210", "messageId": "211", "endLine": 11, "endColumn": 31}, {"ruleId": "185", "severity": 1, "message": "186", "line": 99, "column": 7, "nodeType": "187", "messageId": "188", "endLine": 99, "endColumn": 20, "suggestions": "216"}, {"ruleId": "208", "severity": 1, "message": "217", "line": 4, "column": 7, "nodeType": "210", "messageId": "211", "endLine": 7, "endColumn": 4}, {"ruleId": "185", "severity": 1, "message": "186", "line": 52, "column": 7, "nodeType": "187", "messageId": "188", "endLine": 52, "endColumn": 18, "suggestions": "218"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 61, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 61, "endColumn": 18, "suggestions": "219"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 70, "column": 3, "nodeType": "187", "messageId": "188", "endLine": 70, "endColumn": 14, "suggestions": "220"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 74, "column": 3, "nodeType": "187", "messageId": "188", "endLine": 74, "endColumn": 16, "suggestions": "221"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 78, "column": 3, "nodeType": "187", "messageId": "188", "endLine": 78, "endColumn": 14, "suggestions": "222"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 84, "column": 3, "nodeType": "187", "messageId": "188", "endLine": 84, "endColumn": 14, "suggestions": "223"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 81, "column": 7, "nodeType": "187", "messageId": "188", "endLine": 81, "endColumn": 20, "suggestions": "224"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 261, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 261, "endColumn": 18, "suggestions": "225"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 295, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 295, "endColumn": 18, "suggestions": "226"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 227, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 227, "endColumn": 18, "suggestions": "227"}, {"ruleId": "208", "severity": 1, "message": "228", "line": 1, "column": 10, "nodeType": "210", "messageId": "211", "endLine": 1, "endColumn": 21}, {"ruleId": "185", "severity": 1, "message": "186", "line": 244, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 244, "endColumn": 18, "suggestions": "229"}, {"ruleId": "208", "severity": 1, "message": "228", "line": 1, "column": 10, "nodeType": "210", "messageId": "211", "endLine": 1, "endColumn": 21}, {"ruleId": "185", "severity": 1, "message": "186", "line": 264, "column": 5, "nodeType": "187", "messageId": "188", "endLine": 264, "endColumn": 18, "suggestions": "230"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 59, "column": 7, "nodeType": "187", "messageId": "188", "endLine": 59, "endColumn": 20, "suggestions": "231"}, {"ruleId": "185", "severity": 1, "message": "186", "line": 61, "column": 7, "nodeType": "187", "messageId": "188", "endLine": 61, "endColumn": 20, "suggestions": "232"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["233"], ["234"], ["235"], ["236"], ["237"], ["238"], ["239"], ["240"], ["241"], ["242"], ["243"], ["244"], ["245"], ["246"], ["247"], ["248"], ["249"], ["250"], ["251"], "no-unused-vars", "'debt' is defined but never used.", "Identifier", "unusedVar", "'debtId' is defined but never used.", ["252"], "'product' is defined but never used.", "'productId' is defined but never used.", ["253"], "'mongoose' is defined but never used.", ["254"], ["255"], ["256"], ["257"], ["258"], ["259"], ["260"], ["261"], ["262"], ["263"], "'NextRequest' is defined but never used.", ["264"], ["265"], ["266"], ["267"], {"messageId": "268", "data": "269", "fix": "270", "desc": "271"}, {"messageId": "268", "data": "272", "fix": "273", "desc": "271"}, {"messageId": "268", "data": "274", "fix": "275", "desc": "271"}, {"messageId": "268", "data": "276", "fix": "277", "desc": "271"}, {"messageId": "268", "data": "278", "fix": "279", "desc": "271"}, {"messageId": "268", "data": "280", "fix": "281", "desc": "271"}, {"messageId": "268", "data": "282", "fix": "283", "desc": "271"}, {"messageId": "268", "data": "284", "fix": "285", "desc": "271"}, {"messageId": "268", "data": "286", "fix": "287", "desc": "271"}, {"messageId": "268", "data": "288", "fix": "289", "desc": "271"}, {"messageId": "268", "data": "290", "fix": "291", "desc": "271"}, {"messageId": "268", "data": "292", "fix": "293", "desc": "271"}, {"messageId": "268", "data": "294", "fix": "295", "desc": "271"}, {"messageId": "268", "data": "296", "fix": "297", "desc": "271"}, {"messageId": "268", "data": "298", "fix": "299", "desc": "271"}, {"messageId": "268", "data": "300", "fix": "301", "desc": "271"}, {"messageId": "268", "data": "302", "fix": "303", "desc": "271"}, {"messageId": "268", "data": "304", "fix": "305", "desc": "271"}, {"messageId": "268", "data": "306", "fix": "307", "desc": "271"}, {"messageId": "268", "data": "308", "fix": "309", "desc": "271"}, {"messageId": "268", "data": "310", "fix": "311", "desc": "271"}, {"messageId": "268", "data": "312", "fix": "313", "desc": "314"}, {"messageId": "268", "data": "315", "fix": "316", "desc": "271"}, {"messageId": "268", "data": "317", "fix": "318", "desc": "314"}, {"messageId": "268", "data": "319", "fix": "320", "desc": "271"}, {"messageId": "268", "data": "321", "fix": "322", "desc": "314"}, {"messageId": "268", "data": "323", "fix": "324", "desc": "314"}, {"messageId": "268", "data": "325", "fix": "326", "desc": "271"}, {"messageId": "268", "data": "327", "fix": "328", "desc": "271"}, {"messageId": "268", "data": "329", "fix": "330", "desc": "271"}, {"messageId": "268", "data": "331", "fix": "332", "desc": "271"}, {"messageId": "268", "data": "333", "fix": "334", "desc": "271"}, {"messageId": "268", "data": "335", "fix": "336", "desc": "271"}, {"messageId": "268", "data": "337", "fix": "338", "desc": "271"}, {"messageId": "268", "data": "339", "fix": "340", "desc": "271"}, "removeConsole", {"propertyName": "341"}, {"range": "342", "text": "343"}, "Remove the console.error().", {"propertyName": "341"}, {"range": "344", "text": "343"}, {"propertyName": "341"}, {"range": "345", "text": "343"}, {"propertyName": "341"}, {"range": "346", "text": "343"}, {"propertyName": "341"}, {"range": "347", "text": "343"}, {"propertyName": "341"}, {"range": "348", "text": "343"}, {"propertyName": "341"}, {"range": "349", "text": "343"}, {"propertyName": "341"}, {"range": "350", "text": "343"}, {"propertyName": "341"}, {"range": "351", "text": "343"}, {"propertyName": "341"}, {"range": "352", "text": "343"}, {"propertyName": "341"}, {"range": "353", "text": "343"}, {"propertyName": "341"}, {"range": "354", "text": "343"}, {"propertyName": "341"}, {"range": "355", "text": "343"}, {"propertyName": "341"}, {"range": "356", "text": "343"}, {"propertyName": "341"}, {"range": "357", "text": "343"}, {"propertyName": "341"}, {"range": "358", "text": "343"}, {"propertyName": "341"}, {"range": "359", "text": "343"}, {"propertyName": "341"}, {"range": "360", "text": "343"}, {"propertyName": "341"}, {"range": "361", "text": "343"}, {"propertyName": "341"}, {"range": "362", "text": "343"}, {"propertyName": "341"}, {"range": "363", "text": "343"}, {"propertyName": "364"}, {"range": "365", "text": "343"}, "Remove the console.log().", {"propertyName": "341"}, {"range": "366", "text": "343"}, {"propertyName": "364"}, {"range": "367", "text": "343"}, {"propertyName": "341"}, {"range": "368", "text": "343"}, {"propertyName": "364"}, {"range": "369", "text": "343"}, {"propertyName": "364"}, {"range": "370", "text": "343"}, {"propertyName": "341"}, {"range": "371", "text": "343"}, {"propertyName": "341"}, {"range": "372", "text": "343"}, {"propertyName": "341"}, {"range": "373", "text": "343"}, {"propertyName": "341"}, {"range": "374", "text": "343"}, {"propertyName": "341"}, {"range": "375", "text": "343"}, {"propertyName": "341"}, {"range": "376", "text": "343"}, {"propertyName": "341"}, {"range": "377", "text": "343"}, {"propertyName": "341"}, {"range": "378", "text": "343"}, "error", [3024, 3080], "", [1283, 1329], [2696, 2741], [2911, 2964], [838, 883], [2872, 2917], [3733, 3778], [1249, 1298], [2529, 2577], [844, 892], [2968, 3016], [3851, 3899], [1872, 1918], [2336, 2389], [3224, 3269], [3906, 3961], [1427, 1483], [1426, 1475], [2241, 2289], [3007, 3050], [2486, 2532], "log", [1203, 1241], [1384, 1432], [1592, 1640], [1691, 1742], [1797, 1850], [1960, 2027], [1873, 1923], [7239, 7287], [8055, 8107], [7525, 7575], [8056, 8115], [8753, 8807], [1392, 1446], [1506, 1560]]