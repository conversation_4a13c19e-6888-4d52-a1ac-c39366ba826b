(()=>{var e={};e.id=698,e.ids=[698],e.modules={823:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1132:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\tindahan\\\\app\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\admin\\page.tsx","default")},1135:()=>{},1573:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var r=t(687),a=t(3210),i=t(8962);let d=(0,i.A)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var n=t(8122),l=t(2730);let o=(0,i.A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var c=t(3689);function m(){let[e,s]=(0,a.useState)(null),[t,i]=(0,a.useState)(!1),[m,x]=(0,a.useState)(!1),[h,p]=(0,a.useState)(null),[u,b]=(0,a.useState)(null),g=async()=>{i(!0),b(null);try{let e=await fetch("/api/admin/seed"),t=await e.json();t.success?s(t.data):b(t.error||"Failed to fetch seed status")}catch(e){b("Network error while fetching seed status")}finally{i(!1)}},j=async(e=!1)=>{x(!0),b(null),p(null);try{let s=await fetch("/api/admin/seed",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({clearExisting:e})}),t=await s.json();t.success?(p(t),await g()):b(t.error||"Failed to seed database")}catch(e){b("Network error while seeding database")}finally{x(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(d,{className:"h-8 w-8 text-blue-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Database Administration"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage sample data for the Sari-Sari Store system"})]})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Database Status"}),(0,r.jsxs)("button",{onClick:g,disabled:t,className:"flex items-center space-x-2 px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors disabled:opacity-50",children:[(0,r.jsx)(n.A,{className:`h-4 w-4 ${t?"animate-spin":""}`}),(0,r.jsx)("span",{children:"Refresh"})]})]}),t?(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600",children:[(0,r.jsx)(l.A,{className:"h-4 w-4 animate-spin"}),(0,r.jsx)("span",{children:"Loading status..."})]}):e?(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"Products"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:e.productCount}),(0,r.jsx)("p",{className:"text-sm text-blue-700",children:"Current products in database"})]}),(0,r.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-medium text-green-900 mb-2",children:"Customer Debts"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:e.debtCount}),(0,r.jsx)("p",{className:"text-sm text-green-700",children:"Current debt records"})]})]}):u?(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-red-600",children:[(0,r.jsx)(o,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:u})]}):null]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Sample Data Management"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,r.jsxs)("button",{onClick:()=>j(!1),disabled:m,className:"flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[m?(0,r.jsx)(l.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(c.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Add Sample Data"})]}),(0,r.jsxs)("button",{onClick:()=>j(!0),disabled:m,className:"flex items-center justify-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[m?(0,r.jsx)(l.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(o,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Reset & Seed Database"})]})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Add Sample Data:"})," Adds sample products and debts if database is empty"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Reset & Seed:"})," Clears all existing data and adds fresh sample data"]})]})]})]}),h&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Seeding Results"}),(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 text-green-600"}),(0,r.jsx)("span",{className:"font-medium text-green-800",children:h.message})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"Products"}),(0,r.jsxs)("p",{className:"text-sm text-blue-700",children:["Created: ",h.data.products.created," | Existing: ",h.data.products.existing]})]}),(0,r.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-medium text-green-900 mb-2",children:"Customer Debts"}),(0,r.jsxs)("p",{className:"text-sm text-green-700",children:["Created: ",h.data.debts.created," | Existing: ",h.data.debts.existing]})]})]}),h.data.statistics&&(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Business Statistics"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Total Stock Value:"}),(0,r.jsxs)("p",{className:"font-semibold",children:["₱",h.data.statistics.totalStockValue.toFixed(2)]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Total Debt Amount:"}),(0,r.jsxs)("p",{className:"font-semibold",children:["₱",h.data.statistics.totalDebtAmount.toFixed(2)]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Unpaid Debt:"}),(0,r.jsxs)("p",{className:"font-semibold text-red-600",children:["₱",h.data.statistics.unpaidDebtAmount.toFixed(2)]})]})]})]})]}),u&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(o,{className:"h-5 w-5 text-red-600"}),(0,r.jsxs)("span",{className:"font-medium text-red-800",children:["Error: ",u]})]})})]})})}},2329:(e,s,t)=>{Promise.resolve().then(t.bind(t,1573))},2340:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>d.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var r=t(5239),a=t(8088),i=t(8170),d=t.n(i),n=t(893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(s,l);let o={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\admin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\admin\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},2730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(8962).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3689:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(8962).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3873:e=>{"use strict";e.exports=require("path")},4431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n,metadata:()=>d});var r=t(7413),a=t(5041),i=t.n(a);t(1120),t(1135);let d={title:"Sari-Sari Store Admin Dashboard",description:"Admin dashboard for managing sari-sari store inventory and customer debts"};function n({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:i().className,children:(0,r.jsx)("div",{className:"min-h-screen bg-gray-50",children:e})})})}},5825:()=>{},5881:(e,s,t)=>{Promise.resolve().then(t.bind(t,1132))},8122:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(8962).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},8847:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},8962:(e,s,t)=>{"use strict";t.d(s,{A:()=>d});var r=t(3210),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),d=(e,s)=>{let t=(0,r.forwardRef)(({color:t="currentColor",size:d=24,strokeWidth:n=2,absoluteStrokeWidth:l,className:o="",children:c,...m},x)=>(0,r.createElement)("svg",{ref:x,...a,width:d,height:d,stroke:t,strokeWidth:l?24*Number(n)/Number(d):n,className:["lucide",`lucide-${i(e)}`,o].join(" "),...m},[...s.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(c)?c:[c]]));return t.displayName=`${e}`,t}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9377:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,945],()=>t(2340));module.exports=r})();