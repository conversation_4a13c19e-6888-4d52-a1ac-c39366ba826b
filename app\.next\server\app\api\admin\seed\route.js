(()=>{var e={};e.id=985,e.ids=[985],e.modules={841:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var i=a(6037),n=a.n(i);let r=new i.Schema({customerName:{type:String,required:[!0,"Customer name is required"],trim:!0,maxlength:[100,"Customer name cannot exceed 100 characters"]},productName:{type:String,required:[!0,"Product name is required"],trim:!0,maxlength:[100,"Product name cannot exceed 100 characters"]},productPrice:{type:Number,required:[!0,"Product price is required"],min:[0,"Product price cannot be negative"]},quantity:{type:Number,required:[!0,"Quantity is required"],min:[1,"Quantity must be at least 1"],validate:{validator:function(e){return Number.isInteger(e)&&e>0},message:"Quantity must be a positive integer"}},totalAmount:{type:Number,required:[!0,"Total amount is required"],min:[0,"Total amount cannot be negative"]},dateOfDebt:{type:Date,required:[!0,"Date of debt is required"],default:Date.now},isPaid:{type:Boolean,default:!1},paidDate:{type:Date,default:null},notes:{type:String,trim:!0,maxlength:[500,"Notes cannot exceed 500 characters"],default:""}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});r.index({customerName:1}),r.index({isPaid:1}),r.index({dateOfDebt:-1}),r.index({customerName:1,isPaid:1}),r.virtual("daysSinceDebt").get(function(){let e=new Date,t=new Date(this.dateOfDebt);return Math.ceil(Math.abs(e.getTime()-t.getTime())/864e5)}),r.pre("save",function(e){this.totalAmount=this.productPrice*this.quantity,this.isPaid&&!this.paidDate&&(this.paidDate=new Date),!this.isPaid&&this.paidDate&&(this.paidDate=void 0),e()}),r.statics.getDebtSummaryByCustomer=async function(e){let t=await this.find({customerName:e}).sort({dateOfDebt:-1});return{customerName:e,totalDebt:t.reduce((e,t)=>e+t.totalAmount,0),totalUnpaid:t.filter(e=>!e.isPaid).reduce((e,t)=>e+t.totalAmount,0),debtCount:t.length,unpaidCount:t.filter(e=>!e.isPaid).length,debts:t}};let s=n().models.CustomerDebt||n().model("CustomerDebt",r)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1253:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var i=a(6037),n=a.n(i);let r=new i.Schema({name:{type:String,required:[!0,"Product name is required"],trim:!0,maxlength:[100,"Product name cannot exceed 100 characters"]},image:{type:String,trim:!0,default:""},netWeight:{type:String,required:[!0,"Net weight is required"],trim:!0,maxlength:[50,"Net weight cannot exceed 50 characters"]},price:{type:Number,required:[!0,"Price is required"],min:[0,"Price cannot be negative"],validate:{validator:function(e){return e>=0},message:"Price must be a positive number"}},stockQuantity:{type:Number,required:[!0,"Stock quantity is required"],min:[0,"Stock quantity cannot be negative"],default:0},category:{type:String,required:[!0,"Category is required"],enum:{values:["snacks","canned goods","beverages","personal care","household","condiments","instant foods","dairy","frozen","others"],message:"Invalid category"}}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});r.index({name:1}),r.index({category:1}),r.index({stockQuantity:1}),r.virtual("isLowStock").get(function(){return this.stockQuantity<=5}),r.pre("save",function(e){this.price<0&&(this.price=0),this.stockQuantity<0&&(this.stockQuantity=0),e()});let s=n().models.Product||n().model("Product",r)},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5745:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var i=a(6037),n=a.n(i);let r=process.env.MONGODB_URI||"mongodb://localhost:27017/sari-sari-store";if(!r)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let s=global.mongoose;async function o(){if(s.conn)if(1===n().connection.readyState)return s.conn;else s.conn=null,s.promise=null;s.promise||(s.promise=n().connect(r,{bufferCommands:!1,maxPoolSize:10,serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3,family:4}).then(e=>e.connection));try{s.conn=await s.promise}catch(e){throw s.promise=null,Error("Failed to connect to database")}return s.conn}s||(s=global.mongoose={conn:null,promise:null}),n().connection.on("connected",()=>{}),n().connection.on("error",e=>{}),n().connection.on("disconnected",()=>{}),process.on("SIGINT",async()=>{await n().connection.close(),process.exit(0)});let c=o},6037:e=>{"use strict";e.exports=require("mongoose")},6487:()=>{},6684:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>D,routeModule:()=>h,serverHooks:()=>f,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>b});var i={};a.r(i),a.d(i,{GET:()=>g,POST:()=>p});var n=a(6559),r=a(8088),s=a(7719),o=a(2190),c=a(5745),u=a(1253),d=a(841);let m=[{name:"Lucky Me! Pancit Canton Original",image:"https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400",netWeight:"60g",price:15,stockQuantity:50,category:"instant foods"},{name:"Coca-Cola 330ml",image:"https://images.unsplash.com/photo-1554866585-cd94860890b7?w=400",netWeight:"330ml",price:25,stockQuantity:30,category:"beverages"},{name:"Argentina Corned Beef 150g",image:"https://images.unsplash.com/photo-1544025162-d76694265947?w=400",netWeight:"150g",price:45,stockQuantity:20,category:"canned goods"},{name:"Chippy Barbecue 110g",image:"https://images.unsplash.com/photo-1566478989037-eec170784d0b?w=400",netWeight:"110g",price:35,stockQuantity:25,category:"snacks"},{name:"Safeguard Soap Classic White",image:"https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400",netWeight:"90g",price:18,stockQuantity:15,category:"personal care"},{name:"Tide Powder Detergent 35g",image:"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400",netWeight:"35g",price:12,stockQuantity:40,category:"household"},{name:"Silver Swan Soy Sauce 200ml",image:"https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400",netWeight:"200ml",price:22,stockQuantity:18,category:"condiments"},{name:"Bear Brand Milk Drink 300ml",image:"https://images.unsplash.com/photo-1550583724-b2692b85b150?w=400",netWeight:"300ml",price:28,stockQuantity:12,category:"dairy"},{name:"Skyflakes Crackers 250g",image:"https://images.unsplash.com/photo-1558961363-fa8fdf82db35?w=400",netWeight:"250g",price:32,stockQuantity:22,category:"snacks"},{name:"Century Tuna Flakes in Oil 180g",image:"https://images.unsplash.com/photo-1544025162-d76694265947?w=400",netWeight:"180g",price:38,stockQuantity:16,category:"canned goods"}],l=[{customerName:"Maria Santos",productName:"Lucky Me! Pancit Canton Original",productPrice:15,quantity:3,dateOfDebt:new Date("2024-01-15"),isPaid:!1,notes:"Regular customer, good payment history"},{customerName:"Juan Dela Cruz",productName:"Coca-Cola 330ml",productPrice:25,quantity:2,dateOfDebt:new Date("2024-01-10"),isPaid:!0,paidDate:new Date("2024-01-12"),notes:"Paid on time"},{customerName:"Ana Reyes",productName:"Argentina Corned Beef 150g",productPrice:45,quantity:1,dateOfDebt:new Date("2024-01-18"),isPaid:!1,notes:"Will pay on Friday"},{customerName:"Pedro Garcia",productName:"Chippy Barbecue 110g",productPrice:35,quantity:2,dateOfDebt:new Date("2024-01-12"),isPaid:!0,paidDate:new Date("2024-01-14"),notes:"Paid with exact amount"},{customerName:"Maria Santos",productName:"Bear Brand Milk Drink 300ml",productPrice:28,quantity:1,dateOfDebt:new Date("2024-01-20"),isPaid:!1,notes:"For her child"},{customerName:"Rosa Mendoza",productName:"Safeguard Soap Classic White",productPrice:18,quantity:2,dateOfDebt:new Date("2024-01-16"),isPaid:!1,notes:"New customer"},{customerName:"Carlos Villanueva",productName:"Century Tuna Flakes in Oil 180g",productPrice:38,quantity:1,dateOfDebt:new Date("2024-01-08"),isPaid:!0,paidDate:new Date("2024-01-11"),notes:"Loyal customer"},{customerName:"Luz Fernandez",productName:"Tide Powder Detergent 35g",productPrice:12,quantity:3,dateOfDebt:new Date("2024-01-19"),isPaid:!1,notes:"Bulk purchase"},{customerName:"Ana Reyes",productName:"Silver Swan Soy Sauce 200ml",productPrice:22,quantity:1,dateOfDebt:new Date("2024-01-21"),isPaid:!1,notes:"For cooking"},{customerName:"Juan Dela Cruz",productName:"Skyflakes Crackers 250g",productPrice:32,quantity:1,dateOfDebt:new Date("2024-01-17"),isPaid:!0,paidDate:new Date("2024-01-19"),notes:"Quick payment"}];async function p(e){try{await (0,c.A)();let{clearExisting:t=!1}=await e.json().catch(()=>({})),a={success:!0,message:"Database seeded successfully",data:{products:{created:0,existing:0},debts:{created:0,existing:0},statistics:{totalProducts:0,totalDebts:0,totalStockValue:0,totalDebtAmount:0,unpaidDebtAmount:0}}};t&&(await u.A.deleteMany({}),await d.A.deleteMany({}),a.message+=" (existing data cleared)");let i=await u.A.find({});if(0===i.length){let e=await u.A.insertMany(m);a.data.products.created=e.length}else a.data.products.existing=i.length;let n=await d.A.find({});if(0===n.length){let e=await d.A.insertMany(l);a.data.debts.created=e.length}else a.data.debts.existing=n.length;let r=await u.A.find({}),s=await d.A.find({}),p=r.reduce((e,t)=>e+t.price*t.stockQuantity,0),g=s.reduce((e,t)=>e+t.totalAmount,0),h=s.filter(e=>!e.isPaid).reduce((e,t)=>e+t.totalAmount,0);return a.data.statistics={totalProducts:r.length,totalDebts:s.length,totalStockValue:parseFloat(p.toFixed(2)),totalDebtAmount:parseFloat(g.toFixed(2)),unpaidDebtAmount:parseFloat(h.toFixed(2))},o.NextResponse.json(a)}catch(e){return o.NextResponse.json({success:!1,error:"Failed to seed database",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function g(){try{await (0,c.A)();let e=await u.A.countDocuments(),t=await d.A.countDocuments();return o.NextResponse.json({success:!0,data:{isSeeded:e>0||t>0,productCount:e,debtCount:t,sampleDataAvailable:{products:m.length,debts:l.length}}})}catch(e){return o.NextResponse.json({success:!1,error:"Failed to check seed status"},{status:500})}}let h=new n.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/seed/route",pathname:"/api/admin/seed",filename:"route",bundlePath:"app/api/admin/seed/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\admin\\seed\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:y,workUnitAsyncStorage:b,serverHooks:f}=h;function D(){return(0,s.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:b})}},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[447,580],()=>a(6684));module.exports=i})();