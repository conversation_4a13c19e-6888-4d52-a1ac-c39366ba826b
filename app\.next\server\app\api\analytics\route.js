(()=>{var t={};t.id=70,t.ids=[70],t.modules={841:(t,e,a)=>{"use strict";a.d(e,{A:()=>i});var o=a(6037),n=a.n(o);let r=new o.Schema({customerName:{type:String,required:[!0,"Customer name is required"],trim:!0,maxlength:[100,"Customer name cannot exceed 100 characters"]},productName:{type:String,required:[!0,"Product name is required"],trim:!0,maxlength:[100,"Product name cannot exceed 100 characters"]},productPrice:{type:Number,required:[!0,"Product price is required"],min:[0,"Product price cannot be negative"]},quantity:{type:Number,required:[!0,"Quantity is required"],min:[1,"Quantity must be at least 1"],validate:{validator:function(t){return Number.isInteger(t)&&t>0},message:"Quantity must be a positive integer"}},totalAmount:{type:Number,required:[!0,"Total amount is required"],min:[0,"Total amount cannot be negative"]},dateOfDebt:{type:Date,required:[!0,"Date of debt is required"],default:Date.now},isPaid:{type:Boolean,default:!1},paidDate:{type:Date,default:null},notes:{type:String,trim:!0,maxlength:[500,"Notes cannot exceed 500 characters"],default:""}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});r.index({customerName:1}),r.index({isPaid:1}),r.index({dateOfDebt:-1}),r.index({customerName:1,isPaid:1}),r.virtual("daysSinceDebt").get(function(){let t=new Date,e=new Date(this.dateOfDebt);return Math.ceil(Math.abs(t.getTime()-e.getTime())/864e5)}),r.pre("save",function(t){this.totalAmount=this.productPrice*this.quantity,this.isPaid&&!this.paidDate&&(this.paidDate=new Date),!this.isPaid&&this.paidDate&&(this.paidDate=void 0),t()}),r.statics.getDebtSummaryByCustomer=async function(t){let e=await this.find({customerName:t}).sort({dateOfDebt:-1});return{customerName:t,totalDebt:e.reduce((t,e)=>t+e.totalAmount,0),totalUnpaid:e.filter(t=>!t.isPaid).reduce((t,e)=>t+e.totalAmount,0),debtCount:e.length,unpaidCount:e.filter(t=>!t.isPaid).length,debts:e}};let i=n().models.CustomerDebt||n().model("CustomerDebt",r)},846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1253:(t,e,a)=>{"use strict";a.d(e,{A:()=>i});var o=a(6037),n=a.n(o);let r=new o.Schema({name:{type:String,required:[!0,"Product name is required"],trim:!0,maxlength:[100,"Product name cannot exceed 100 characters"]},image:{type:String,trim:!0,default:""},netWeight:{type:String,required:[!0,"Net weight is required"],trim:!0,maxlength:[50,"Net weight cannot exceed 50 characters"]},price:{type:Number,required:[!0,"Price is required"],min:[0,"Price cannot be negative"],validate:{validator:function(t){return t>=0},message:"Price must be a positive number"}},stockQuantity:{type:Number,required:[!0,"Stock quantity is required"],min:[0,"Stock quantity cannot be negative"],default:0},category:{type:String,required:[!0,"Category is required"],enum:{values:["snacks","canned goods","beverages","personal care","household","condiments","instant foods","dairy","frozen","others"],message:"Invalid category"}}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});r.index({name:1}),r.index({category:1}),r.index({stockQuantity:1}),r.virtual("isLowStock").get(function(){return this.stockQuantity<=5}),r.pre("save",function(t){this.price<0&&(this.price=0),this.stockQuantity<0&&(this.stockQuantity=0),t()});let i=n().models.Product||n().model("Product",r)},3033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5745:(t,e,a)=>{"use strict";a.d(e,{A:()=>s});var o=a(6037),n=a.n(o);let r=process.env.MONGODB_URI||"mongodb://localhost:27017/sari-sari-store";if(!r)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let i=global.mongoose;async function u(){if(i.conn)if(1===n().connection.readyState)return i.conn;else i.conn=null,i.promise=null;i.promise||(i.promise=n().connect(r,{bufferCommands:!1,maxPoolSize:10,serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3,family:4}).then(t=>t.connection));try{i.conn=await i.promise}catch(t){throw i.promise=null,Error("Failed to connect to database")}return i.conn}i||(i=global.mongoose={conn:null,promise:null}),n().connection.on("connected",()=>{}),n().connection.on("error",t=>{}),n().connection.on("disconnected",()=>{}),process.on("SIGINT",async()=>{await n().connection.close(),process.exit(0)});let s=u},6037:t=>{"use strict";t.exports=require("mongoose")},6487:()=>{},8335:()=>{},9294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9819:(t,e,a)=>{"use strict";a.r(e),a.d(e,{patchFetch:()=>y,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>$});var o={};a.r(o),a.d(o,{GET:()=>m});var n=a(6559),r=a(8088),i=a(7719),u=a(2190),s=a(5745),d=a(1253),c=a(841);async function m(t){try{await (0,s.A)();let{searchParams:e}=new URL(t.url),a=e.get("period")||"30",o=parseInt(a),n=new Date;n.setDate(n.getDate()-o);let r=await d.A.aggregate([{$group:{_id:"$category",totalProducts:{$sum:1},totalStockValue:{$sum:{$multiply:["$price","$stockQuantity"]}},averagePrice:{$avg:"$price"},totalStock:{$sum:"$stockQuantity"},lowStockCount:{$sum:{$cond:[{$lte:["$stockQuantity",5]},1,0]}},outOfStockCount:{$sum:{$cond:[{$eq:["$stockQuantity",0]},1,0]}}}},{$project:{category:"$_id",totalProducts:1,totalStockValue:{$round:["$totalStockValue",2]},averagePrice:{$round:["$averagePrice",2]},totalStock:1,lowStockCount:1,outOfStockCount:1,_id:0}},{$sort:{totalStockValue:-1}}]),i=await c.A.aggregate([{$group:{_id:"$customerName",totalDebts:{$sum:1},totalAmount:{$sum:"$totalAmount"},unpaidAmount:{$sum:{$cond:[{$eq:["$isPaid",!1]},"$totalAmount",0]}},unpaidCount:{$sum:{$cond:[{$eq:["$isPaid",!1]},1,0]}},lastDebtDate:{$max:"$dateOfDebt"},averageDebtAmount:{$avg:"$totalAmount"},paymentRate:{$avg:{$cond:[{$eq:["$isPaid",!0]},1,0]}}}},{$project:{customerName:"$_id",totalDebts:1,totalAmount:{$round:["$totalAmount",2]},unpaidAmount:{$round:["$unpaidAmount",2]},unpaidCount:1,lastDebtDate:1,averageDebtAmount:{$round:["$averageDebtAmount",2]},paymentRate:{$round:[{$multiply:["$paymentRate",100]},1]},_id:0}},{$sort:{unpaidAmount:-1}}]),m=await c.A.aggregate([{$group:{_id:"$productName",totalSold:{$sum:"$quantity"},totalRevenue:{$sum:"$totalAmount"},averagePrice:{$avg:"$productPrice"},uniqueCustomers:{$addToSet:"$customerName"},lastSoldDate:{$max:"$dateOfDebt"}}},{$project:{productName:"$_id",totalSold:1,totalRevenue:{$round:["$totalRevenue",2]},averagePrice:{$round:["$averagePrice",2]},uniqueCustomers:{$size:"$uniqueCustomers"},lastSoldDate:1,_id:0}},{$sort:{totalRevenue:-1}},{$limit:10}]),l=new Date;l.setDate(l.getDate()-7);let p=(await c.A.find({isPaid:!1,dateOfDebt:{$lt:l}}).select("customerName productName totalAmount dateOfDebt notes").sort({dateOfDebt:1}).lean()).map(t=>({...t,daysOverdue:Math.floor((Date.now()-new Date(t.dateOfDebt).getTime())/864e5)})),$=await c.A.aggregate([{$match:{dateOfDebt:{$gte:new Date(Date.now()-15552e6)}}},{$group:{_id:{year:{$year:"$dateOfDebt"},month:{$month:"$dateOfDebt"}},totalDebts:{$sum:1},totalAmount:{$sum:"$totalAmount"},paidDebts:{$sum:{$cond:[{$eq:["$isPaid",!0]},1,0]}},paidAmount:{$sum:{$cond:[{$eq:["$isPaid",!0]},"$totalAmount",0]}}}},{$project:{month:"$_id.month",year:"$_id.year",totalDebts:1,totalAmount:{$round:["$totalAmount",2]},paidDebts:1,paidAmount:{$round:["$paidAmount",2]},paymentRate:{$round:[{$multiply:[{$divide:["$paidDebts","$totalDebts"]},100]},1]},_id:0}},{$sort:{year:1,month:1}}]),g=i.filter(t=>t.unpaidAmount>100||t.paymentRate<50||t.unpaidCount>3).map(t=>({...t,riskLevel:t.unpaidAmount>200?"high":t.unpaidAmount>100?"medium":"low",riskFactors:[...t.unpaidAmount>100?["High unpaid amount"]:[],...t.paymentRate<50?["Low payment rate"]:[],...t.unpaidCount>3?["Multiple unpaid debts"]:[]]})),y={totalRevenue:i.reduce((t,e)=>t+e.totalAmount,0),totalUnpaidAmount:i.reduce((t,e)=>t+e.unpaidAmount,0),totalInventoryValue:r.reduce((t,e)=>t+e.totalStockValue,0),averageDebtAmount:i.length>0?i.reduce((t,e)=>t+e.averageDebtAmount,0)/i.length:0,paymentRate:i.length>0?i.reduce((t,e)=>t+e.paymentRate,0)/i.length:0},v={inventoryAnalytics:r,customerAnalytics:i.slice(0,10),productPerformance:m,overdueDebts:p,monthlyTrends:$,riskCustomers:g,businessSummary:{...y,totalRevenue:Math.round(100*y.totalRevenue)/100,totalUnpaidAmount:Math.round(100*y.totalUnpaidAmount)/100,totalInventoryValue:Math.round(100*y.totalInventoryValue)/100,averageDebtAmount:Math.round(100*y.averageDebtAmount)/100,paymentRate:Math.round(10*y.paymentRate)/10},generatedAt:new Date().toISOString(),period:`${o} days`};return u.NextResponse.json({success:!0,data:v})}catch(t){return u.NextResponse.json({success:!1,error:"Failed to fetch analytics data",details:t instanceof Error?t.message:"Unknown error"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/analytics/route",pathname:"/api/analytics",filename:"route",bundlePath:"app/api/analytics/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\analytics\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:p,workUnitAsyncStorage:$,serverHooks:g}=l;function y(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:$})}}};var e=require("../../../webpack-runtime.js");e.C(t);var a=t=>e(e.s=t),o=e.X(0,[447,580],()=>a(9819));module.exports=o})();