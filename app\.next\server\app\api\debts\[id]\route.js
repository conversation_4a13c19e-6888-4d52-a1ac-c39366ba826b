(()=>{var e={};e.id=874,e.ids=[874],e.modules={841:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(6037),n=s.n(r);let a=new r.Schema({customerName:{type:String,required:[!0,"Customer name is required"],trim:!0,maxlength:[100,"Customer name cannot exceed 100 characters"]},productName:{type:String,required:[!0,"Product name is required"],trim:!0,maxlength:[100,"Product name cannot exceed 100 characters"]},productPrice:{type:Number,required:[!0,"Product price is required"],min:[0,"Product price cannot be negative"]},quantity:{type:Number,required:[!0,"Quantity is required"],min:[1,"Quantity must be at least 1"],validate:{validator:function(e){return Number.isInteger(e)&&e>0},message:"Quantity must be a positive integer"}},totalAmount:{type:Number,required:[!0,"Total amount is required"],min:[0,"Total amount cannot be negative"]},dateOfDebt:{type:Date,required:[!0,"Date of debt is required"],default:Date.now},isPaid:{type:Boolean,default:!1},paidDate:{type:Date,default:null},notes:{type:String,trim:!0,maxlength:[500,"Notes cannot exceed 500 characters"],default:""}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({customerName:1}),a.index({isPaid:1}),a.index({dateOfDebt:-1}),a.index({customerName:1,isPaid:1}),a.virtual("daysSinceDebt").get(function(){let e=new Date,t=new Date(this.dateOfDebt);return Math.ceil(Math.abs(e.getTime()-t.getTime())/864e5)}),a.pre("save",function(e){this.totalAmount=this.productPrice*this.quantity,this.isPaid&&!this.paidDate&&(this.paidDate=new Date),!this.isPaid&&this.paidDate&&(this.paidDate=void 0),e()}),a.statics.getDebtSummaryByCustomer=async function(e){let t=await this.find({customerName:e}).sort({dateOfDebt:-1});return{customerName:e,totalDebt:t.reduce((e,t)=>e+t.totalAmount,0),totalUnpaid:t.filter(e=>!e.isPaid).reduce((e,t)=>e+t.totalAmount,0),debtCount:t.length,unpaidCount:t.filter(e=>!e.isPaid).length,debts:t}};let i=n().models.CustomerDebt||n().model("CustomerDebt",a)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5745:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(6037),n=s.n(r);let a=process.env.MONGODB_URI||"mongodb://localhost:27017/sari-sari-store";if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let i=global.mongoose;async function o(){if(i.conn)if(1===n().connection.readyState)return i.conn;else i.conn=null,i.promise=null;i.promise||(i.promise=n().connect(a,{bufferCommands:!1,maxPoolSize:10,serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3,family:4}).then(e=>e.connection));try{i.conn=await i.promise}catch(e){throw i.promise=null,Error("Failed to connect to database")}return i.conn}i||(i=global.mongoose={conn:null,promise:null}),n().connection.on("connected",()=>{}),n().connection.on("error",e=>{}),n().connection.on("disconnected",()=>{}),process.on("SIGINT",async()=>{await n().connection.close(),process.exit(0)});let u=o},5762:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>h,routeModule:()=>b,serverHooks:()=>D,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>y});var r={};s.r(r),s.d(r,{DELETE:()=>f,GET:()=>p,PUT:()=>m});var n=s(6559),a=s(8088),i=s(7719),o=s(2190),u=s(5745),d=s(841),c=s(6037),l=s.n(c);async function p(e,{params:t}){try{if(await (0,u.A)(),!l().Types.ObjectId.isValid(t.id))return o.NextResponse.json({success:!1,error:"Invalid debt ID"},{status:400});let e=await d.A.findById(t.id);if(!e)return o.NextResponse.json({success:!1,error:"Debt not found"},{status:404});return o.NextResponse.json({success:!0,data:e})}catch(e){return o.NextResponse.json({success:!1,error:"Failed to fetch debt"},{status:500})}}async function m(e,{params:t}){try{if(await (0,u.A)(),!l().Types.ObjectId.isValid(t.id))return o.NextResponse.json({success:!1,error:"Invalid debt ID"},{status:400});let{customerName:s,productName:r,productPrice:n,quantity:a,dateOfDebt:i,isPaid:c,notes:p}=await e.json();if(!s||!r||!n||!a)return o.NextResponse.json({success:!1,error:"Missing required fields"},{status:400});if(n<=0||a<=0)return o.NextResponse.json({success:!1,error:"Price and quantity must be positive"},{status:400});let m={customerName:s.trim(),productName:r.trim(),productPrice:parseFloat(n),quantity:parseInt(a),dateOfDebt:i?new Date(i):new Date,notes:p||"",isPaid:!!c};c&&!m.paidDate?m.paidDate=new Date:c||(m.paidDate=null);let f=await d.A.findByIdAndUpdate(t.id,m,{new:!0,runValidators:!0});if(!f)return o.NextResponse.json({success:!1,error:"Debt not found"},{status:404});return o.NextResponse.json({success:!0,data:f,message:"Debt updated successfully"})}catch(e){return o.NextResponse.json({success:!1,error:"Failed to update debt"},{status:500})}}async function f(e,{params:t}){try{if(await (0,u.A)(),!l().Types.ObjectId.isValid(t.id))return o.NextResponse.json({success:!1,error:"Invalid debt ID"},{status:400});if(!await d.A.findByIdAndDelete(t.id))return o.NextResponse.json({success:!1,error:"Debt not found"},{status:404});return o.NextResponse.json({success:!0,message:"Debt deleted successfully"})}catch(e){return o.NextResponse.json({success:!1,error:"Failed to delete debt"},{status:500})}}let b=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/debts/[id]/route",pathname:"/api/debts/[id]",filename:"route",bundlePath:"app/api/debts/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:x,workUnitAsyncStorage:y,serverHooks:D}=b;function h(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:y})}},6037:e=>{"use strict";e.exports=require("mongoose")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580],()=>s(5762));module.exports=r})();