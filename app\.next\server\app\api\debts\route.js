(()=>{var e={};e.id=168,e.ids=[168],e.modules={841:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(6037),n=r.n(s);let a=new s.Schema({customerName:{type:String,required:[!0,"Customer name is required"],trim:!0,maxlength:[100,"Customer name cannot exceed 100 characters"]},productName:{type:String,required:[!0,"Product name is required"],trim:!0,maxlength:[100,"Product name cannot exceed 100 characters"]},productPrice:{type:Number,required:[!0,"Product price is required"],min:[0,"Product price cannot be negative"]},quantity:{type:Number,required:[!0,"Quantity is required"],min:[1,"Quantity must be at least 1"],validate:{validator:function(e){return Number.isInteger(e)&&e>0},message:"Quantity must be a positive integer"}},totalAmount:{type:Number,required:[!0,"Total amount is required"],min:[0,"Total amount cannot be negative"]},dateOfDebt:{type:Date,required:[!0,"Date of debt is required"],default:Date.now},isPaid:{type:Boolean,default:!1},paidDate:{type:Date,default:null},notes:{type:String,trim:!0,maxlength:[500,"Notes cannot exceed 500 characters"],default:""}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({customerName:1}),a.index({isPaid:1}),a.index({dateOfDebt:-1}),a.index({customerName:1,isPaid:1}),a.virtual("daysSinceDebt").get(function(){let e=new Date,t=new Date(this.dateOfDebt);return Math.ceil(Math.abs(e.getTime()-t.getTime())/864e5)}),a.pre("save",function(e){this.totalAmount=this.productPrice*this.quantity,this.isPaid&&!this.paidDate&&(this.paidDate=new Date),!this.isPaid&&this.paidDate&&(this.paidDate=void 0),e()}),a.statics.getDebtSummaryByCustomer=async function(e){let t=await this.find({customerName:e}).sort({dateOfDebt:-1});return{customerName:e,totalDebt:t.reduce((e,t)=>e+t.totalAmount,0),totalUnpaid:t.filter(e=>!e.isPaid).reduce((e,t)=>e+t.totalAmount,0),debtCount:t.length,unpaidCount:t.filter(e=>!e.isPaid).length,debts:t}};let i=n().models.CustomerDebt||n().model("CustomerDebt",a)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2520:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>l});var n=r(6559),a=r(8088),i=r(7719),o=r(2190),u=r(5745),c=r(841);async function d(e){try{await (0,u.A)();let{searchParams:t}=new URL(e.url),r=t.get("customer"),s=t.get("isPaid"),n=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),i=(n-1)*a,d={};r&&(d.customerName={$regex:r,$options:"i"}),null!=s&&(d.isPaid="true"===s);let l=await c.A.find(d).sort({dateOfDebt:-1}).skip(i).limit(a),p=await c.A.countDocuments(d);return o.NextResponse.json({success:!0,data:l,pagination:{page:n,limit:a,total:p,pages:Math.ceil(p/a)}})}catch(e){return o.NextResponse.json({success:!1,error:"Failed to fetch debts"},{status:500})}}async function l(e){try{await (0,u.A)();let{customerName:t,productName:r,productPrice:s,quantity:n,dateOfDebt:a,notes:i}=await e.json();if(!t||!r||!s||!n)return o.NextResponse.json({success:!1,error:"Missing required fields"},{status:400});if(s<=0||n<=0)return o.NextResponse.json({success:!1,error:"Price and quantity must be positive"},{status:400});let d=new c.A({customerName:t.trim(),productName:r.trim(),productPrice:parseFloat(s),quantity:parseInt(n),dateOfDebt:a?new Date(a):new Date,notes:i||"",isPaid:!1});return await d.save(),o.NextResponse.json({success:!0,data:d,message:"Debt created successfully"},{status:201})}catch(e){return o.NextResponse.json({success:!1,error:"Failed to create debt"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/debts/route",pathname:"/api/debts",filename:"route",bundlePath:"app/api/debts/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:h}=p;function b(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5745:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(6037),n=r.n(s);let a=process.env.MONGODB_URI||"mongodb://localhost:27017/sari-sari-store";if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let i=global.mongoose;async function o(){if(i.conn)if(1===n().connection.readyState)return i.conn;else i.conn=null,i.promise=null;i.promise||(i.promise=n().connect(a,{bufferCommands:!1,maxPoolSize:10,serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3,family:4}).then(e=>e.connection));try{i.conn=await i.promise}catch(e){throw i.promise=null,Error("Failed to connect to database")}return i.conn}i||(i=global.mongoose={conn:null,promise:null}),n().connection.on("connected",()=>{}),n().connection.on("error",e=>{}),n().connection.on("disconnected",()=>{}),process.on("SIGINT",async()=>{await n().connection.close(),process.exit(0)});let u=o},6037:e=>{"use strict";e.exports=require("mongoose")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(2520));module.exports=s})();