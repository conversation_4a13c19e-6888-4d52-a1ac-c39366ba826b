(()=>{var e={};e.id=173,e.ids=[173],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2676:(e,s,t)=>{Promise.resolve().then(t.bind(t,5037))},2730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(8962).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3555:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\tindahan\\\\app\\\\src\\\\app\\\\notifications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\notifications\\page.tsx","default")},3873:e=>{"use strict";e.exports=require("path")},5037:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var r=t(687),a=t(3210),i=t(2254),n=t(9080),l=t(228),d=t(1312),o=t(7051),c=t(2730),x=t(8122),m=t(8492),h=t(2953),u=t(5814),p=t.n(u);function g(){let[e,s]=(0,a.useState)(null),[t,u]=(0,a.useState)(!0),[g,j]=(0,a.useState)("all"),[b,v]=(0,a.useState)("all"),f=async()=>{u(!0);try{let e=await fetch("/api/notifications"),t=await e.json();t.success&&s(t.data)}catch(e){}finally{u(!1)}},y=e=>{switch(e){case"low_stock":case"out_of_stock":return(0,r.jsx)(n.A,{className:"h-6 w-6"});case"overdue_debt":case"payment_reminder":return(0,r.jsx)(l.A,{className:"h-6 w-6"});case"high_risk_customer":return(0,r.jsx)(d.A,{className:"h-6 w-6"});default:return(0,r.jsx)(o.A,{className:"h-6 w-6"})}},N=e=>{switch(e){case"critical":return"text-red-600 bg-red-50 border-red-200";case"high":return"text-orange-600 bg-orange-50 border-orange-200";case"medium":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"low":return"text-blue-600 bg-blue-50 border-blue-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},w=e=>({critical:"bg-red-100 text-red-800",high:"bg-orange-100 text-orange-800",medium:"bg-yellow-100 text-yellow-800",low:"bg-blue-100 text-blue-800"})[e]||"bg-gray-100 text-gray-800",_=e=>{switch(e.type){case"low_stock":case"out_of_stock":return"/products";case"overdue_debt":case"payment_reminder":case"high_risk_customer":return"/debts";default:return null}},k=e?.notifications.filter(e=>{let s="all"===g||e.severity===g,t="all"===b||e.type===b;return s&&t})||[];return t?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(i.A,{}),(0,r.jsx)("div",{className:"flex items-center justify-center py-20",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.A,{className:"h-8 w-8 animate-spin text-blue-600"}),(0,r.jsx)("span",{className:"text-lg text-gray-600",children:"Loading notifications..."})]})})]}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(i.A,{}),(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Notifications & Alerts"}),(0,r.jsx)("p",{className:"text-gray-600",children:"System alerts and business notifications"})]}),(0,r.jsxs)("button",{onClick:f,className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mt-4 sm:mt-0",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Refresh"})]})]}),e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.total}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Total"})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-red-600",children:e.summary.critical}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Critical"})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:e.summary.high}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"High"})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:e.summary.medium}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Medium"})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:e.summary.low}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Low"})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border mb-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 text-gray-600"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Severity:"}),(0,r.jsxs)("select",{value:g,onChange:e=>j(e.target.value),className:"px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"All"}),(0,r.jsx)("option",{value:"critical",children:"Critical"}),(0,r.jsx)("option",{value:"high",children:"High"}),(0,r.jsx)("option",{value:"medium",children:"Medium"}),(0,r.jsx)("option",{value:"low",children:"Low"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Type:"}),(0,r.jsxs)("select",{value:b,onChange:e=>v(e.target.value),className:"px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"All"}),(0,r.jsx)("option",{value:"low_stock",children:"Low Stock"}),(0,r.jsx)("option",{value:"out_of_stock",children:"Out of Stock"}),(0,r.jsx)("option",{value:"overdue_debt",children:"Overdue Debt"}),(0,r.jsx)("option",{value:"high_risk_customer",children:"High Risk Customer"}),(0,r.jsx)("option",{value:"payment_reminder",children:"Payment Reminder"})]})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",k.length," of ",e.summary.total," notifications"]})]})}),(0,r.jsx)("div",{className:"space-y-4",children:k.length>0?k.map(e=>{let s=_(e);return(0,r.jsx)("div",{className:`bg-white rounded-lg p-6 shadow-sm border-l-4 ${N(e.severity)}`,children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:y(e.type)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.title}),(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${w(e.severity)}`,children:e.severity.toUpperCase()})]}),(0,r.jsx)("p",{className:"text-gray-700 mb-3",children:e.message}),e.data&&(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3 text-sm",children:["low_stock"===e.type&&(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,r.jsxs)("div",{children:["Category: ",(0,r.jsx)("span",{className:"font-medium",children:e.data.category})]}),(0,r.jsxs)("div",{children:["Price: ",(0,r.jsxs)("span",{className:"font-medium",children:["₱",e.data.price.toFixed(2)]})]})]}),"overdue_debt"===e.type&&(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,r.jsxs)("div",{children:["Debt Count: ",(0,r.jsx)("span",{className:"font-medium",children:e.data.debtCount})]}),(0,r.jsxs)("div",{children:["Days Overdue: ",(0,r.jsx)("span",{className:"font-medium",children:e.data.daysOverdue})]})]}),"high_risk_customer"===e.type&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-1",children:["Payment Rate: ",(0,r.jsxs)("span",{className:"font-medium",children:[e.data.paymentRate,"%"]})]}),(0,r.jsxs)("div",{children:["Risk Factors: ",(0,r.jsx)("span",{className:"font-medium",children:e.data.riskFactors.join(", ")})]})]})]})]})]}),s&&(0,r.jsxs)(p(),{href:s,className:"flex items-center space-x-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:[(0,r.jsx)("span",{children:"Take Action"}),(0,r.jsx)(h.A,{className:"h-4 w-4"})]})]})},e.id)}):(0,r.jsxs)("div",{className:"bg-white rounded-lg p-8 shadow-sm border text-center",children:[(0,r.jsx)(o.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No notifications found"}),(0,r.jsx)("p",{className:"text-gray-600",children:"No notifications match your current filters."})]})})]})]})]})}},6644:(e,s,t)=>{Promise.resolve().then(t.bind(t,3555))},7836:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=t(5239),a=t(8088),i=t(8170),n=t.n(i),l=t(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o={children:["",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3555)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\notifications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\notifications\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/notifications/page",pathname:"/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},8122:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(8962).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},8492:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(8962).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,945,73,188],()=>t(7836));module.exports=r})();