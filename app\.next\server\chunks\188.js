exports.id=188,exports.ids=[188],exports.modules={823:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},1135:()=>{},2254:(e,s,t)=>{"use strict";t.d(s,{A:()=>f});var r=t(687),a=t(5814),i=t.n(a),l=t(6189),n=t(7897),c=t(9080),d=t(1312),o=t(4808),m=t(3210),x=t(228),h=t(7051),u=t(1860),b=t(2953),j=t(3589),v=t(5891);function g(){let[e,s]=(0,m.useState)(null),[t,a]=(0,m.useState)(!1),[l,n]=(0,m.useState)(!1),[o,g]=(0,m.useState)(new Set),f=e=>{switch(e){case"low_stock":case"out_of_stock":return(0,r.jsx)(c.A,{className:"h-5 w-5"});case"overdue_debt":case"payment_reminder":return(0,r.jsx)(x.A,{className:"h-5 w-5"});case"high_risk_customer":return(0,r.jsx)(d.A,{className:"h-5 w-5"});default:return(0,r.jsx)(h.A,{className:"h-5 w-5"})}},y=e=>{switch(e){case"critical":return"text-red-600 bg-red-50 border-red-200";case"high":return"text-orange-600 bg-orange-50 border-orange-200";case"medium":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"low":return"text-blue-600 bg-blue-50 border-blue-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},N=e=>{switch(e.type){case"low_stock":case"out_of_stock":return"/products";case"overdue_debt":case"payment_reminder":case"high_risk_customer":return"/debts";default:return null}},p=e=>{let s=new Set(o);s.has(e)?s.delete(e):s.add(e),g(s)},w=(e?.summary.critical||0)+(e?.summary.high||0);return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>a(!t),className:"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg",children:[(0,r.jsx)(h.A,{className:"h-6 w-6"}),w>0&&(0,r.jsx)("span",{className:"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:w>9?"9+":w})]}),t&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden",children:[(0,r.jsxs)("div",{className:"px-4 py-3 border-b border-gray-200 flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Notifications"}),e&&(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.summary.total," total • ",w," urgent"]})]}),(0,r.jsx)("button",{onClick:()=>a(!1),className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(u.A,{className:"h-5 w-5"})})]}),(0,r.jsx)("div",{className:"max-h-80 overflow-y-auto",children:l?(0,r.jsxs)("div",{className:"p-4 text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Loading notifications..."})]}):e&&e.notifications.length>0?(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:e.notifications.slice(0,10).map(e=>{let s=o.has(e.id),t=N(e);return(0,r.jsx)("div",{className:`p-4 ${y(e.severity)}`,children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:f(e.type)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("p",{className:"text-sm font-medium truncate",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[t&&(0,r.jsx)(i(),{href:t,className:"text-xs hover:underline flex items-center",onClick:()=>a(!1),children:(0,r.jsx)(b.A,{className:"h-3 w-3"})}),(0,r.jsx)("button",{onClick:()=>p(e.id),className:"text-xs hover:underline",children:s?(0,r.jsx)(j.A,{className:"h-3 w-3"}):(0,r.jsx)(v.A,{className:"h-3 w-3"})})]})]}),(0,r.jsx)("p",{className:"text-sm mt-1",children:e.message}),s&&e.data&&(0,r.jsxs)("div",{className:"mt-2 text-xs space-y-1",children:["low_stock"===e.type&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:["Category: ",e.data.category]}),(0,r.jsxs)("p",{children:["Price: ₱",e.data.price.toFixed(2)]})]}),"overdue_debt"===e.type&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:["Debt Count: ",e.data.debtCount]}),(0,r.jsxs)("p",{children:["Days Overdue: ",e.data.daysOverdue]})]}),"high_risk_customer"===e.type&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:["Payment Rate: ",e.data.paymentRate,"%"]}),(0,r.jsxs)("p",{children:["Risk Factors: ",e.data.riskFactors.join(", ")]})]})]})]})]})},e.id)})}):(0,r.jsxs)("div",{className:"p-4 text-center",children:[(0,r.jsx)(h.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"No notifications"})]})}),e&&e.notifications.length>10&&(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 text-center",children:(0,r.jsxs)(i(),{href:"/notifications",className:"text-sm text-blue-600 hover:text-blue-800",onClick:()=>a(!1),children:["View all ",e.summary.total," notifications"]})})]})]})}function f(){let e=(0,l.usePathname)(),s=[{href:"/",label:"Dashboard",icon:n.A,active:"/"===e},{href:"/products",label:"Products",icon:c.A,active:"/products"===e},{href:"/debts",label:"Customer Debts",icon:d.A,active:"/debts"===e},{href:"/analytics",label:"Analytics",icon:o.A,active:"/analytics"===e}];return(0,r.jsx)("nav",{className:"border-b bg-white shadow-sm",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,r.jsxs)(i(),{href:"/",className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-blue-600 to-blue-700 shadow-md",children:(0,r.jsx)("span",{className:"text-lg font-bold text-white",children:"\uD83C\uDFEA"})}),(0,r.jsxs)("div",{className:"hidden sm:block",children:[(0,r.jsx)("span",{className:"text-lg font-bold text-gray-900",children:"Sari-Sari Store"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Admin Dashboard"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"flex space-x-1",children:s.map(e=>{let s=e.icon;return(0,r.jsxs)(i(),{href:e.href,className:`flex items-center space-x-2 rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200 ${e.active?"bg-blue-100 text-blue-700 shadow-sm":"text-gray-600 hover:bg-gray-100 hover:text-gray-900 hover:shadow-sm"}`,children:[(0,r.jsx)(s,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:e.label})]},e.href)})}),(0,r.jsx)(g,{})]})]})})})}},4431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n,metadata:()=>l});var r=t(7413),a=t(5041),i=t.n(a);t(1120),t(1135);let l={title:"Sari-Sari Store Admin Dashboard",description:"Admin dashboard for managing sari-sari store inventory and customer debts"};function n({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:i().className,children:(0,r.jsx)("div",{className:"min-h-screen bg-gray-50",children:e})})})}},5825:()=>{},8847:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},9377:()=>{}};