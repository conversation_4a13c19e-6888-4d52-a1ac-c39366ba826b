"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[838],{838:(e,s,t)=>{t.d(s,{A:()=>y});var a=t(5155),r=t(6874),l=t.n(r),i=t(5695),c=t(8199),n=t(7108),d=t(7580),o=t(4576),x=t(2115),h=t(9074),m=t(3861),u=t(4416),b=t(3786),j=t(7863),f=t(6474);function g(){let[e,s]=(0,x.useState)(null),[t,r]=(0,x.useState)(!1),[i,c]=(0,x.useState)(!1),[o,g]=(0,x.useState)(new Set);(0,x.useEffect)(()=>{y();let e=setInterval(y,3e5);return()=>clearInterval(e)},[]);let y=async()=>{c(!0);try{let e=await fetch("/api/notifications"),t=await e.json();t.success&&s(t.data)}catch(e){}finally{c(!1)}},v=e=>{switch(e){case"low_stock":case"out_of_stock":return(0,a.jsx)(n.A,{className:"h-5 w-5"});case"overdue_debt":case"payment_reminder":return(0,a.jsx)(h.A,{className:"h-5 w-5"});case"high_risk_customer":return(0,a.jsx)(d.A,{className:"h-5 w-5"});default:return(0,a.jsx)(m.A,{className:"h-5 w-5"})}},N=e=>{switch(e){case"critical":return"text-red-600 bg-red-50 border-red-200";case"high":return"text-orange-600 bg-orange-50 border-orange-200";case"medium":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"low":return"text-blue-600 bg-blue-50 border-blue-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},p=e=>{switch(e.type){case"low_stock":case"out_of_stock":return"/products";case"overdue_debt":case"payment_reminder":case"high_risk_customer":return"/debts";default:return null}},w=e=>{let s=new Set(o);s.has(e)?s.delete(e):s.add(e),g(s)},_=((null==e?void 0:e.summary.critical)||0)+((null==e?void 0:e.summary.high)||0);return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>r(!t),className:"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg",children:[(0,a.jsx)(m.A,{className:"h-6 w-6"}),_>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:_>9?"9+":_})]}),t&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden",children:[(0,a.jsxs)("div",{className:"px-4 py-3 border-b border-gray-200 flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Notifications"}),e&&(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.summary.total," total • ",_," urgent"]})]}),(0,a.jsx)("button",{onClick:()=>r(!1),className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(u.A,{className:"h-5 w-5"})})]}),(0,a.jsx)("div",{className:"max-h-80 overflow-y-auto",children:i?(0,a.jsxs)("div",{className:"p-4 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Loading notifications..."})]}):e&&e.notifications.length>0?(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:e.notifications.slice(0,10).map(e=>{let s=o.has(e.id),t=p(e);return(0,a.jsx)("div",{className:"p-4 ".concat(N(e.severity)),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:v(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-sm font-medium truncate",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[t&&(0,a.jsx)(l(),{href:t,className:"text-xs hover:underline flex items-center",onClick:()=>r(!1),children:(0,a.jsx)(b.A,{className:"h-3 w-3"})}),(0,a.jsx)("button",{onClick:()=>w(e.id),className:"text-xs hover:underline",children:s?(0,a.jsx)(j.A,{className:"h-3 w-3"}):(0,a.jsx)(f.A,{className:"h-3 w-3"})})]})]}),(0,a.jsx)("p",{className:"text-sm mt-1",children:e.message}),s&&e.data&&(0,a.jsxs)("div",{className:"mt-2 text-xs space-y-1",children:["low_stock"===e.type&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:["Category: ",e.data.category]}),(0,a.jsxs)("p",{children:["Price: ₱",e.data.price.toFixed(2)]})]}),"overdue_debt"===e.type&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:["Debt Count: ",e.data.debtCount]}),(0,a.jsxs)("p",{children:["Days Overdue: ",e.data.daysOverdue]})]}),"high_risk_customer"===e.type&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{children:["Payment Rate: ",e.data.paymentRate,"%"]}),(0,a.jsxs)("p",{children:["Risk Factors: ",e.data.riskFactors.join(", ")]})]})]})]})]})},e.id)})}):(0,a.jsxs)("div",{className:"p-4 text-center",children:[(0,a.jsx)(m.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"No notifications"})]})}),e&&e.notifications.length>10&&(0,a.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 text-center",children:(0,a.jsxs)(l(),{href:"/notifications",className:"text-sm text-blue-600 hover:text-blue-800",onClick:()=>r(!1),children:["View all ",e.summary.total," notifications"]})})]})]})}function y(){let e=(0,i.usePathname)(),s=[{href:"/",label:"Dashboard",icon:c.A,active:"/"===e},{href:"/products",label:"Products",icon:n.A,active:"/products"===e},{href:"/debts",label:"Customer Debts",icon:d.A,active:"/debts"===e},{href:"/analytics",label:"Analytics",icon:o.A,active:"/analytics"===e}];return(0,a.jsx)("nav",{className:"border-b bg-white shadow-sm",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,a.jsxs)(l(),{href:"/",className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-blue-600 to-blue-700 shadow-md",children:(0,a.jsx)("span",{className:"text-lg font-bold text-white",children:"\uD83C\uDFEA"})}),(0,a.jsxs)("div",{className:"hidden sm:block",children:[(0,a.jsx)("span",{className:"text-lg font-bold text-gray-900",children:"Sari-Sari Store"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Admin Dashboard"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"flex space-x-1",children:s.map(e=>{let s=e.icon;return(0,a.jsxs)(l(),{href:e.href,className:"flex items-center space-x-2 rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200 ".concat(e.active?"bg-blue-100 text-blue-700 shadow-sm":"text-gray-600 hover:bg-gray-100 hover:text-gray-900 hover:shadow-sm"),children:[(0,a.jsx)(s,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:e.label})]},e.href)})}),(0,a.jsx)(g,{})]})]})})})}}}]);