(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{1207:(e,s,t)=>{Promise.resolve().then(t.bind(t,5839))},2895:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var a=t(5594),r=t(376),l=t(7414),d=t(2115),i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),c=(e,s)=>{let t=(0,d.forwardRef)((t,c)=>{var{color:o="currentColor",size:x=24,strokeWidth:m=2,absoluteStrokeWidth:h,className:u="",children:p}=t,b=(0,l._)(t,["color","size","strokeWidth","absoluteStrokeWidth","className","children"]);return(0,d.createElement)("svg",(0,a._)((0,r._)((0,a._)({ref:c},i),{width:x,height:x,stroke:o,strokeWidth:h?24*Number(m)/Number(x):m,className:["lucide","lucide-".concat(n(e)),u].join(" ")}),b),[...s.map(e=>{let[s,t]=e;return(0,d.createElement)(s,t)}),...Array.isArray(p)?p:[p]])});return t.displayName="".concat(e),t}},2915:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3904:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},5839:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(5155),r=t(2115),l=t(2895);let d=(0,l.A)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var i=t(3904),n=t(7624);let c=(0,l.A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var o=t(2915);function x(){let[e,s]=(0,r.useState)(null),[t,l]=(0,r.useState)(!1),[x,m]=(0,r.useState)(!1),[h,u]=(0,r.useState)(null),[p,b]=(0,r.useState)(null);(0,r.useEffect)(()=>{g()},[]);let g=async()=>{l(!0),b(null);try{let e=await fetch("/api/admin/seed"),t=await e.json();t.success?s(t.data):b(t.error||"Failed to fetch seed status")}catch(e){b("Network error while fetching seed status")}finally{l(!1)}},j=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];m(!0),b(null),u(null);try{let s=await fetch("/api/admin/seed",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({clearExisting:e})}),t=await s.json();t.success?(u(t),await g()):b(t.error||"Failed to seed database")}catch(e){b("Network error while seeding database")}finally{m(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(d,{className:"h-8 w-8 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Database Administration"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage sample data for the Sari-Sari Store system"})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Database Status"}),(0,a.jsxs)("button",{onClick:g,disabled:t,className:"flex items-center space-x-2 px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors disabled:opacity-50",children:[(0,a.jsx)(i.A,{className:"h-4 w-4 ".concat(t?"animate-spin":"")}),(0,a.jsx)("span",{children:"Refresh"})]})]}),t?(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600",children:[(0,a.jsx)(n.A,{className:"h-4 w-4 animate-spin"}),(0,a.jsx)("span",{children:"Loading status..."})]}):e?(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"Products"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:e.productCount}),(0,a.jsx)("p",{className:"text-sm text-blue-700",children:"Current products in database"})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"font-medium text-green-900 mb-2",children:"Customer Debts"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:e.debtCount}),(0,a.jsx)("p",{className:"text-sm text-green-700",children:"Current debt records"})]})]}):p?(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-red-600",children:[(0,a.jsx)(c,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:p})]}):null]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Sample Data Management"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,a.jsxs)("button",{onClick:()=>j(!1),disabled:x,className:"flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[x?(0,a.jsx)(n.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(o.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Add Sample Data"})]}),(0,a.jsxs)("button",{onClick:()=>j(!0),disabled:x,className:"flex items-center justify-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[x?(0,a.jsx)(n.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(c,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Reset & Seed Database"})]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Add Sample Data:"})," Adds sample products and debts if database is empty"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Reset & Seed:"})," Clears all existing data and adds fresh sample data"]})]})]})]}),h&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Seeding Results"}),(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("span",{className:"font-medium text-green-800",children:h.message})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"Products"}),(0,a.jsxs)("p",{className:"text-sm text-blue-700",children:["Created: ",h.data.products.created," | Existing: ",h.data.products.existing]})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"font-medium text-green-900 mb-2",children:"Customer Debts"}),(0,a.jsxs)("p",{className:"text-sm text-green-700",children:["Created: ",h.data.debts.created," | Existing: ",h.data.debts.existing]})]})]}),h.data.statistics&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Business Statistics"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Total Stock Value:"}),(0,a.jsxs)("p",{className:"font-semibold",children:["₱",h.data.statistics.totalStockValue.toFixed(2)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Total Debt Amount:"}),(0,a.jsxs)("p",{className:"font-semibold",children:["₱",h.data.statistics.totalDebtAmount.toFixed(2)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Unpaid Debt:"}),(0,a.jsxs)("p",{className:"font-semibold text-red-600",children:["₱",h.data.statistics.unpaidDebtAmount.toFixed(2)]})]})]})]})]}),p&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c,{className:"h-5 w-5 text-red-600"}),(0,a.jsxs)("span",{className:"font-medium text-red-800",children:["Error: ",p]})]})})]})})}},7414:(e,s,t)=>{"use strict";function a(e,s){if(null==e)return{};var t,a,r=function(e,s){if(null==e)return{};var t,a,r={},l=Object.keys(e);for(a=0;a<l.length;a++)t=l[a],s.indexOf(t)>=0||(r[t]=e[t]);return r}(e,s);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(a=0;a<l.length;a++)t=l[a],!(s.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(r[t]=e[t])}return r}t.r(s),t.d(s,{_:()=>a})},7624:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(1207)),_N_E=e.O()}]);