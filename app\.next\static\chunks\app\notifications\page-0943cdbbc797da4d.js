(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[173],{1420:(e,s,t)=>{Promise.resolve().then(t.bind(t,7435))},1482:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},3904:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},7435:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var a=t(5155),l=t(2115),r=t(838),i=t(7108),c=t(9074),d=t(7580),n=t(3861),o=t(7624),x=t(3904),m=t(1482),h=t(3786),u=t(6874),g=t.n(u);function j(){let[e,s]=(0,l.useState)(null),[t,u]=(0,l.useState)(!0),[j,p]=(0,l.useState)("all"),[b,y]=(0,l.useState)("all");(0,l.useEffect)(()=>{v()},[]);let v=async()=>{u(!0);try{let e=await fetch("/api/notifications"),t=await e.json();t.success&&s(t.data)}catch(e){}finally{u(!1)}},N=e=>{switch(e){case"low_stock":case"out_of_stock":return(0,a.jsx)(i.A,{className:"h-6 w-6"});case"overdue_debt":case"payment_reminder":return(0,a.jsx)(c.A,{className:"h-6 w-6"});case"high_risk_customer":return(0,a.jsx)(d.A,{className:"h-6 w-6"});default:return(0,a.jsx)(n.A,{className:"h-6 w-6"})}},f=e=>{switch(e){case"critical":return"text-red-600 bg-red-50 border-red-200";case"high":return"text-orange-600 bg-orange-50 border-orange-200";case"medium":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"low":return"text-blue-600 bg-blue-50 border-blue-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},w=e=>({critical:"bg-red-100 text-red-800",high:"bg-orange-100 text-orange-800",medium:"bg-yellow-100 text-yellow-800",low:"bg-blue-100 text-blue-800"})[e]||"bg-gray-100 text-gray-800",_=e=>{switch(e.type){case"low_stock":case"out_of_stock":return"/products";case"overdue_debt":case"payment_reminder":case"high_risk_customer":return"/debts";default:return null}},k=(null==e?void 0:e.notifications.filter(e=>{let s="all"===j||e.severity===j,t="all"===b||e.type===b;return s&&t}))||[];return t?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(r.A,{}),(0,a.jsx)("div",{className:"flex items-center justify-center py-20",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.A,{className:"h-8 w-8 animate-spin text-blue-600"}),(0,a.jsx)("span",{className:"text-lg text-gray-600",children:"Loading notifications..."})]})})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(r.A,{}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Notifications & Alerts"}),(0,a.jsx)("p",{className:"text-gray-600",children:"System alerts and business notifications"})]}),(0,a.jsxs)("button",{onClick:v,className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mt-4 sm:mt-0",children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Refresh"})]})]}),e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.summary.total}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total"})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-red-600",children:e.summary.critical}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Critical"})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:e.summary.high}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"High"})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:e.summary.medium}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Medium"})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:e.summary.low}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Low"})]})})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border mb-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 text-gray-600"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Severity:"}),(0,a.jsxs)("select",{value:j,onChange:e=>p(e.target.value),className:"px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All"}),(0,a.jsx)("option",{value:"critical",children:"Critical"}),(0,a.jsx)("option",{value:"high",children:"High"}),(0,a.jsx)("option",{value:"medium",children:"Medium"}),(0,a.jsx)("option",{value:"low",children:"Low"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Type:"}),(0,a.jsxs)("select",{value:b,onChange:e=>y(e.target.value),className:"px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All"}),(0,a.jsx)("option",{value:"low_stock",children:"Low Stock"}),(0,a.jsx)("option",{value:"out_of_stock",children:"Out of Stock"}),(0,a.jsx)("option",{value:"overdue_debt",children:"Overdue Debt"}),(0,a.jsx)("option",{value:"high_risk_customer",children:"High Risk Customer"}),(0,a.jsx)("option",{value:"payment_reminder",children:"Payment Reminder"})]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",k.length," of ",e.summary.total," notifications"]})]})}),(0,a.jsx)("div",{className:"space-y-4",children:k.length>0?k.map(e=>{let s=_(e);return(0,a.jsx)("div",{className:"bg-white rounded-lg p-6 shadow-sm border-l-4 ".concat(f(e.severity)),children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:N(e.type)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.title}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(w(e.severity)),children:e.severity.toUpperCase()})]}),(0,a.jsx)("p",{className:"text-gray-700 mb-3",children:e.message}),e.data&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3 text-sm",children:["low_stock"===e.type&&(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsxs)("div",{children:["Category: ",(0,a.jsx)("span",{className:"font-medium",children:e.data.category})]}),(0,a.jsxs)("div",{children:["Price: ",(0,a.jsxs)("span",{className:"font-medium",children:["₱",e.data.price.toFixed(2)]})]})]}),"overdue_debt"===e.type&&(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsxs)("div",{children:["Debt Count: ",(0,a.jsx)("span",{className:"font-medium",children:e.data.debtCount})]}),(0,a.jsxs)("div",{children:["Days Overdue: ",(0,a.jsx)("span",{className:"font-medium",children:e.data.daysOverdue})]})]}),"high_risk_customer"===e.type&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-1",children:["Payment Rate: ",(0,a.jsxs)("span",{className:"font-medium",children:[e.data.paymentRate,"%"]})]}),(0,a.jsxs)("div",{children:["Risk Factors: ",(0,a.jsx)("span",{className:"font-medium",children:e.data.riskFactors.join(", ")})]})]})]})]})]}),s&&(0,a.jsxs)(g(),{href:s,className:"flex items-center space-x-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:[(0,a.jsx)("span",{children:"Take Action"}),(0,a.jsx)(h.A,{className:"h-4 w-4"})]})]})},e.id)}):(0,a.jsxs)("div",{className:"bg-white rounded-lg p-8 shadow-sm border text-center",children:[(0,a.jsx)(n.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No notifications found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"No notifications match your current filters."})]})})]})]})]})}},7624:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[737,838,441,684,358],()=>s(1420)),_N_E=e.O()}]);