(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{809:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1028:(e,s,t)=>{Promise.resolve().then(t.bind(t,3792))},3109:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},3792:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var a=t(5155),l=t(2115),d=t(6874),r=t.n(d),i=t(7108),c=t(7580),n=t(809),m=t(5868),x=t(4576),o=t(3861),h=t(9074),g=t(3109),u=t(838);function j(){let[e,s]=(0,l.useState)(null),[t,d]=(0,l.useState)(!0);(0,l.useEffect)(()=>{j()},[]);let j=async()=>{try{let e=await fetch("/api/dashboard/stats"),t=await e.json();t.success&&s(t.data)}catch(e){}finally{d(!1)}},b=e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric"});return t?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(u.A,{}),(0,a.jsx)("div",{className:"flex items-center justify-center py-20",children:(0,a.jsx)("div",{className:"h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"})})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(u.A,{}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"mb-2 text-3xl font-bold text-gray-900",children:"Dashboard Overview"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Monitor your sari-sari store performance and key metrics"})]}),(0,a.jsxs)("div",{className:"mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsx)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i.A,{className:"mr-3 h-8 w-8 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Products"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==e?void 0:e.products.totalProducts)||0})]})]})}),(0,a.jsx)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"mr-3 h-8 w-8 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Customers"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==e?void 0:e.debts.totalCustomers)||0})]})]})}),(0,a.jsx)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.A,{className:"mr-3 h-8 w-8 text-red-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Unpaid Debts"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==e?void 0:e.debts.totalUnpaidDebts)||0})]})]})}),(0,a.jsx)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(m.A,{className:"mr-3 h-8 w-8 text-orange-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Unpaid Amount"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₱",(null==e?void 0:e.debts.totalUnpaidAmount.toFixed(2))||"0.00"]})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-3",children:[(0,a.jsxs)("div",{className:"space-y-6 lg:col-span-2",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,a.jsx)(r(),{href:"/products",className:"block",children:(0,a.jsxs)("div",{className:"rounded-lg border-l-4 border-blue-500 bg-white p-6 shadow-sm transition-shadow duration-300 hover:shadow-md",children:[(0,a.jsxs)("div",{className:"mb-4 flex items-center",children:[(0,a.jsx)(i.A,{className:"mr-3 h-8 w-8 text-blue-600"}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Product Management"})]}),(0,a.jsx)("p",{className:"mb-4 text-gray-600",children:"Manage inventory, track stock levels, and update product information."}),(0,a.jsx)("div",{className:"font-semibold text-blue-600",children:"Manage Products →"})]})}),(0,a.jsx)(r(),{href:"/debts",className:"block",children:(0,a.jsxs)("div",{className:"rounded-lg border-l-4 border-green-500 bg-white p-6 shadow-sm transition-shadow duration-300 hover:shadow-md",children:[(0,a.jsxs)("div",{className:"mb-4 flex items-center",children:[(0,a.jsx)(c.A,{className:"mr-3 h-8 w-8 text-green-600"}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Customer Debts"})]}),(0,a.jsx)("p",{className:"mb-4 text-gray-600",children:"Track customer debts (utang) and manage payment records."}),(0,a.jsx)("div",{className:"font-semibold text-green-600",children:"Manage Debts →"})]})}),(0,a.jsx)(r(),{href:"/analytics",className:"block",children:(0,a.jsxs)("div",{className:"rounded-lg border-l-4 border-purple-500 bg-white p-6 shadow-sm transition-shadow duration-300 hover:shadow-md",children:[(0,a.jsxs)("div",{className:"mb-4 flex items-center",children:[(0,a.jsx)(x.A,{className:"mr-3 h-8 w-8 text-purple-600"}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Business Analytics"})]}),(0,a.jsx)("p",{className:"mb-4 text-gray-600",children:"Advanced insights, trends, and performance analytics."}),(0,a.jsx)("div",{className:"font-semibold text-purple-600",children:"View Analytics →"})]})})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[(0,a.jsxs)("div",{className:"mb-4 flex items-center",children:[(0,a.jsx)(o.A,{className:"mr-2 h-6 w-6 text-orange-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Advanced Features"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsxs)(r(),{href:"/notifications",className:"flex items-center justify-between rounded-lg bg-orange-50 p-4 hover:bg-orange-100 transition-colors",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Smart Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Alerts for low stock, overdue debts, and risk management"})]}),(0,a.jsx)(o.A,{className:"h-5 w-5 text-orange-600"})]}),(0,a.jsxs)(r(),{href:"/admin",className:"flex items-center justify-between rounded-lg bg-blue-50 p-4 hover:bg-blue-100 transition-colors",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Database Management"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Seed sample data and manage database"})]}),(0,a.jsx)(i.A,{className:"h-5 w-5 text-blue-600"})]})]})]}),(null==e?void 0:e.lowStockProducts)&&e.lowStockProducts.length>0&&(0,a.jsxs)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[(0,a.jsxs)("div",{className:"mb-4 flex items-center",children:[(0,a.jsx)(n.A,{className:"mr-2 h-6 w-6 text-red-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Low Stock Alert"})]}),(0,a.jsx)("div",{className:"space-y-3",children:e.lowStockProducts.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-red-50 p-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsx)("p",{className:"text-sm capitalize text-gray-600",children:e.category})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"text-sm font-medium text-red-600",children:[e.stockQuantity," left"]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["₱",e.price.toFixed(2)]})]})]},e._id))}),(0,a.jsx)(r(),{href:"/products",className:"mt-4 inline-block font-medium text-blue-600 hover:text-blue-800",children:"View all products →"})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[(0,a.jsxs)("div",{className:"mb-4 flex items-center",children:[(0,a.jsx)(h.A,{className:"mr-2 h-6 w-6 text-blue-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Debts"})]}),(null==e?void 0:e.recentDebts)&&e.recentDebts.length>0?(0,a.jsx)("div",{className:"space-y-3",children:e.recentDebts.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-start justify-between rounded-lg bg-gray-50 p-3",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.customerName}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:e.productName}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:b(e.dateOfDebt)})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:["₱",e.totalAmount.toFixed(2)]}),(0,a.jsx)("span",{className:"rounded-full px-2 py-1 text-xs ".concat(e.isPaid?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.isPaid?"Paid":"Unpaid"})]})]},e._id))}):(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"No recent debt records"}),(0,a.jsx)(r(),{href:"/debts",className:"mt-4 inline-block font-medium text-blue-600 hover:text-blue-800",children:"View all debts →"})]}),(null==e?void 0:e.topCustomers)&&e.topCustomers.length>0&&(0,a.jsxs)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[(0,a.jsxs)("div",{className:"mb-4 flex items-center",children:[(0,a.jsx)(g.A,{className:"mr-2 h-6 w-6 text-orange-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Top Debtors"})]}),(0,a.jsx)("div",{className:"space-y-3",children:e.topCustomers.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-orange-50 p-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.customerName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.debtCount," debt",e.debtCount>1?"s":""]})]}),(0,a.jsxs)("p",{className:"text-sm font-bold text-orange-600",children:["₱",e.totalUnpaid.toFixed(2)]})]},e.customerName))}),(0,a.jsx)(r(),{href:"/debts",className:"mt-4 inline-block font-medium text-blue-600 hover:text-blue-800",children:"View debt summary →"})]})]})]})]})]})}},5868:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[737,838,441,684,358],()=>s(1028)),_N_E=e.O()}]);