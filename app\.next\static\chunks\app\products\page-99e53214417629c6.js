(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[571],{2228:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(5594),a=s(5155),l=s(2115),n=s(4616),c=s(7924),i=s(1482),o=s(7108);let d=["snacks","canned goods","beverages","personal care","household","condiments","instant foods","dairy","frozen","others"];var u=s(376),m=s(4416),x=s(4229);function g(e){let{product:t,onSubmit:s,onCancel:n}=e,[c,i]=(0,l.useState)({name:"",image:"",netWeight:"",price:"",stockQuantity:"",category:"snacks"}),[o,g]=(0,l.useState)(!1),[h,p]=(0,l.useState)({});(0,l.useEffect)(()=>{t&&i({name:t.name,image:t.image||"",netWeight:t.netWeight,price:t.price.toString(),stockQuantity:t.stockQuantity.toString(),category:t.category})},[t]);let b=()=>{let e={};return c.name.trim()||(e.name="Product name is required"),c.netWeight.trim()||(e.netWeight="Net weight is required"),(!c.price||0>parseFloat(c.price))&&(e.price="Valid price is required"),(!c.stockQuantity||0>parseInt(c.stockQuantity))&&(e.stockQuantity="Valid stock quantity is required"),c.category||(e.category="Category is required"),p(e),0===Object.keys(e).length},y=async e=>{if(e.preventDefault(),b()){g(!0);try{let e=t?"/api/products/".concat(t._id):"/api/products",a=t?"PUT":"POST",l=await fetch(e,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify((0,u._)((0,r._)({},c),{price:parseFloat(c.price),stockQuantity:parseInt(c.stockQuantity)}))}),n=await l.json();n.success?s():alert(n.error||"Failed to save product")}catch(e){alert("Error saving product")}finally{g(!1)}}},f=e=>{let{name:t,value:s}=e.target;i(e=>(0,u._)((0,r._)({},e),{[t]:s})),h[t]&&p(e=>(0,u._)((0,r._)({},e),{[t]:""}))};return(0,a.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4",children:(0,a.jsxs)("div",{className:"max-h-[90vh] w-full max-w-md overflow-y-auto rounded-lg bg-white shadow-xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between border-b p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:t?"Edit Product":"Add New Product"}),(0,a.jsx)("button",{onClick:n,className:"text-gray-400 transition-colors hover:text-gray-600",children:(0,a.jsx)(m.A,{className:"h-6 w-6"})})]}),(0,a.jsxs)("form",{onSubmit:y,className:"space-y-4 p-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"mb-1 block text-sm font-medium text-gray-700",children:"Product Name *"}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",value:c.name,onChange:f,className:"w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ".concat(h.name?"border-red-500":"border-gray-300"),placeholder:"Enter product name"}),h.name&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-500",children:h.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"image",className:"mb-1 block text-sm font-medium text-gray-700",children:"Product Image URL"}),(0,a.jsx)("input",{type:"url",id:"image",name:"image",value:c.image,onChange:f,className:"w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500",placeholder:"https://example.com/image.jpg"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"netWeight",className:"mb-1 block text-sm font-medium text-gray-700",children:"Net Weight *"}),(0,a.jsx)("input",{type:"text",id:"netWeight",name:"netWeight",value:c.netWeight,onChange:f,className:"w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ".concat(h.netWeight?"border-red-500":"border-gray-300"),placeholder:"e.g., 100g, 1L, 500ml"}),h.netWeight&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-500",children:h.netWeight})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"price",className:"mb-1 block text-sm font-medium text-gray-700",children:"Price (PHP) *"}),(0,a.jsx)("input",{type:"number",id:"price",name:"price",value:c.price,onChange:f,step:"0.01",min:"0",className:"w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ".concat(h.price?"border-red-500":"border-gray-300"),placeholder:"0.00"}),h.price&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-500",children:h.price})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"stockQuantity",className:"mb-1 block text-sm font-medium text-gray-700",children:"Stock Quantity *"}),(0,a.jsx)("input",{type:"number",id:"stockQuantity",name:"stockQuantity",value:c.stockQuantity,onChange:f,min:"0",className:"w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ".concat(h.stockQuantity?"border-red-500":"border-gray-300"),placeholder:"0"}),h.stockQuantity&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-500",children:h.stockQuantity})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"category",className:"mb-1 block text-sm font-medium text-gray-700",children:"Category *"}),(0,a.jsx)("select",{id:"category",name:"category",value:c.category,onChange:f,className:"w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ".concat(h.category?"border-red-500":"border-gray-300"),children:d.map(e=>(0,a.jsx)("option",{value:e,children:e.charAt(0).toUpperCase()+e.slice(1)},e))}),h.category&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-500",children:h.category})]}),(0,a.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,a.jsx)("button",{type:"button",onClick:n,className:"flex-1 rounded-lg border border-gray-300 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:o,className:"flex flex-1 items-center justify-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700 disabled:opacity-50",children:o?(0,a.jsx)("div",{className:"h-4 w-4 animate-spin rounded-full border-b-2 border-white"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),t?"Update":"Create"]})})]})]})]})})}var h=s(809),p=s(4621),b=s(2525),y=s(6766);let f=(0,l.memo)(function(e){let{product:t,onEdit:s,onDelete:r}=e,n=t.stockQuantity<=5,c=(0,l.useCallback)(()=>{s(t)},[s,t]),i=(0,l.useCallback)(()=>{t._id&&r(t._id)},[r,t._id]);return(0,a.jsxs)("div",{className:"overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md",children:[(0,a.jsxs)("div",{className:"relative h-48 bg-gray-100",children:[t.image?(0,a.jsx)(y.default,{src:t.image,alt:t.name,fill:!0,className:"object-cover"}):(0,a.jsx)("div",{className:"flex h-full w-full items-center justify-center text-gray-400",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto mb-2 flex h-16 w-16 items-center justify-center rounded-lg bg-gray-200",children:"\uD83D\uDCE6"}),(0,a.jsx)("span",{className:"text-sm",children:"No Image"})]})}),n&&(0,a.jsxs)("div",{className:"absolute right-2 top-2 flex items-center gap-1 rounded-full bg-red-500 px-2 py-1 text-xs text-white",children:[(0,a.jsx)(h.A,{className:"h-3 w-3"}),"Low Stock"]})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("h3",{className:"mb-1 line-clamp-2 font-semibold text-gray-900",children:t.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:t.netWeight})]}),(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsxs)("div",{className:"mb-1 flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["₱",t.price.toFixed(2)]}),(0,a.jsxs)("span",{className:"rounded-full px-2 py-1 text-sm ".concat(n?"bg-red-100 text-red-800":0===t.stockQuantity?"bg-gray-100 text-gray-800":"bg-green-100 text-green-800"),children:[t.stockQuantity," in stock"]})]}),(0,a.jsx)("div",{className:"text-xs capitalize text-gray-500",children:t.category})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:c,className:"flex flex-1 items-center justify-center gap-1 rounded-lg bg-blue-50 px-3 py-2 text-sm font-medium text-blue-600 transition-colors hover:bg-blue-100",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),"Edit"]}),(0,a.jsxs)("button",{onClick:i,className:"flex flex-1 items-center justify-center gap-1 rounded-lg bg-red-50 px-3 py-2 text-sm font-medium text-red-600 transition-colors hover:bg-red-100",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),"Delete"]})]})]})]})});var j=s(838);function N(){let[e,t]=(0,l.useState)([]),[s,u]=(0,l.useState)(!0),[m,x]=(0,l.useState)(!1),[h,p]=(0,l.useState)(null),[b,y]=(0,l.useState)(""),[N,v]=(0,l.useState)("all"),[w,k]=(0,l.useState)(1),[C,S]=(0,l.useState)(1),A=(0,l.useCallback)(async()=>{try{u(!0);let e=new URLSearchParams((0,r._)({page:w.toString(),limit:"12"},b&&{search:b},"all"!==N&&{category:N})),s=await fetch("/api/products?".concat(e)),a=await s.json();a.success&&(t(a.data),S(a.pagination.pages))}catch(e){}finally{u(!1)}},[w,b,N]);(0,l.useEffect)(()=>{A()},[A]);let Q=()=>{p(null),x(!0)},_=e=>{p(e),x(!0)},P=async e=>{if(confirm("Are you sure you want to delete this product?"))try{(await fetch("/api/products/".concat(e),{method:"DELETE"})).ok?A():alert("Failed to delete product")}catch(e){alert("Error deleting product")}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(j.A,{}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-8 flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"mb-2 text-3xl font-bold text-gray-900",children:"Product Inventory"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage your store products and inventory"})]}),(0,a.jsxs)("button",{onClick:Q,className:"flex items-center gap-2 rounded-lg bg-blue-600 px-6 py-3 text-white transition-colors hover:bg-blue-700",children:[(0,a.jsx)(n.A,{className:"h-5 w-5"}),"Add Product"]})]}),(0,a.jsx)("div",{className:"mb-8 rounded-lg bg-white p-6 shadow-sm",children:(0,a.jsxs)("div",{className:"flex flex-col gap-4 md:flex-row",children:[(0,a.jsx)("form",{onSubmit:e=>{e.preventDefault(),k(1),A()},className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search products...",value:b,onChange:e=>y(e.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-transparent focus:ring-2 focus:ring-blue-500"})]})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.A,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("select",{value:N,onChange:e=>{v(e.target.value),k(1)},className:"rounded-lg border border-gray-300 px-4 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Categories"}),d.map(e=>(0,a.jsx)("option",{value:e,children:e.charAt(0).toUpperCase()+e.slice(1)},e))]})]})]})}),s?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)("div",{className:"h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"})}):0===e.length?(0,a.jsxs)("div",{className:"py-12 text-center",children:[(0,a.jsx)(o.A,{className:"mx-auto mb-4 h-16 w-16 text-gray-400"}),(0,a.jsx)("h3",{className:"mb-2 text-xl font-semibold text-gray-900",children:"No products found"}),(0,a.jsx)("p",{className:"mb-6 text-gray-600",children:"Get started by adding your first product"}),(0,a.jsxs)("button",{onClick:Q,className:"inline-flex items-center gap-2 rounded-lg bg-blue-600 px-6 py-3 text-white hover:bg-blue-700",children:[(0,a.jsx)(n.A,{className:"h-5 w-5"}),"Add Product"]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:e.map(e=>(0,a.jsx)(f,{product:e,onEdit:_,onDelete:P},e._id))}),C>1&&(0,a.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>k(e=>Math.max(e-1,1)),disabled:1===w,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50",children:"Previous"}),Array.from({length:C},(e,t)=>t+1).map(e=>(0,a.jsx)("button",{onClick:()=>k(e),className:"rounded-lg border px-4 py-2 ".concat(w===e?"border-blue-600 bg-blue-600 text-white":"border-gray-300 hover:bg-gray-50"),children:e},e)),(0,a.jsx)("button",{onClick:()=>k(e=>Math.min(e+1,C)),disabled:w===C,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50",children:"Next"})]})]})]}),m&&(0,a.jsx)(g,{product:h,onSubmit:()=>{x(!1),p(null),A()},onCancel:()=>{x(!1),p(null)}})]})}},6410:(e,t,s)=>{Promise.resolve().then(s.bind(s,2228))}},e=>{var t=t=>e(e.s=t);e.O(0,[737,825,838,441,684,358],()=>t(6410)),_N_E=e.O()}]);