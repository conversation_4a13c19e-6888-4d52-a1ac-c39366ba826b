#!/usr/bin/env tsx

/**
 * 🏪 Sari-Sari Store Database Seeder
 * 
 * Professional seed script to populate the database with realistic sample data
 * for testing and demonstration purposes.
 * 
 * Usage: npm run seed
 */

import mongoose from 'mongoose';
import Product from '../src/lib/models/Product';
import CustomerDebt from '../src/lib/models/CustomerDebt';
import connectDB from '../src/lib/mongodb';

// Sample Products Data - Realistic Filipino Sari-Sari Store Inventory
const sampleProducts = [
  {
    name: 'Lucky Me! Pancit Canton Original',
    image: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400',
    netWeight: '60g',
    price: 15.00,
    stockQuantity: 50,
    category: 'instant foods'
  },
  {
    name: 'Coca-Cola 330ml',
    image: 'https://images.unsplash.com/photo-1554866585-cd94860890b7?w=400',
    netWeight: '330ml',
    price: 25.00,
    stockQuantity: 30,
    category: 'beverages'
  },
  {
    name: 'Argentina Corned Beef 150g',
    image: 'https://images.unsplash.com/photo-1544025162-d76694265947?w=400',
    netWeight: '150g',
    price: 45.00,
    stockQuantity: 20,
    category: 'canned goods'
  },
  {
    name: 'Chippy Barbecue 110g',
    image: 'https://images.unsplash.com/photo-1566478989037-eec170784d0b?w=400',
    netWeight: '110g',
    price: 35.00,
    stockQuantity: 25,
    category: 'snacks'
  },
  {
    name: 'Safeguard Soap Classic White',
    image: 'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400',
    netWeight: '90g',
    price: 18.00,
    stockQuantity: 15,
    category: 'personal care'
  },
  {
    name: 'Tide Powder Detergent 35g',
    image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
    netWeight: '35g',
    price: 12.00,
    stockQuantity: 40,
    category: 'household'
  },
  {
    name: 'Silver Swan Soy Sauce 200ml',
    image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400',
    netWeight: '200ml',
    price: 22.00,
    stockQuantity: 18,
    category: 'condiments'
  },
  {
    name: 'Bear Brand Milk Drink 300ml',
    image: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?w=400',
    netWeight: '300ml',
    price: 28.00,
    stockQuantity: 12,
    category: 'dairy'
  },
  {
    name: 'Skyflakes Crackers 250g',
    image: 'https://images.unsplash.com/photo-1558961363-fa8fdf82db35?w=400',
    netWeight: '250g',
    price: 32.00,
    stockQuantity: 22,
    category: 'snacks'
  },
  {
    name: 'Century Tuna Flakes in Oil 180g',
    image: 'https://images.unsplash.com/photo-1544025162-d76694265947?w=400',
    netWeight: '180g',
    price: 38.00,
    stockQuantity: 16,
    category: 'canned goods'
  }
];

// Sample Customer Debts Data - Realistic Utang Scenarios
const sampleDebts = [
  {
    customerName: 'Maria Santos',
    productName: 'Lucky Me! Pancit Canton Original',
    productPrice: 15.00,
    quantity: 3,
    dateOfDebt: new Date('2024-01-15'),
    isPaid: false,
    notes: 'Regular customer, good payment history'
  },
  {
    customerName: 'Juan Dela Cruz',
    productName: 'Coca-Cola 330ml',
    productPrice: 25.00,
    quantity: 2,
    dateOfDebt: new Date('2024-01-10'),
    isPaid: true,
    paidDate: new Date('2024-01-12'),
    notes: 'Paid on time'
  },
  {
    customerName: 'Ana Reyes',
    productName: 'Argentina Corned Beef 150g',
    productPrice: 45.00,
    quantity: 1,
    dateOfDebt: new Date('2024-01-18'),
    isPaid: false,
    notes: 'Will pay on Friday'
  },
  {
    customerName: 'Pedro Garcia',
    productName: 'Chippy Barbecue 110g',
    productPrice: 35.00,
    quantity: 2,
    dateOfDebt: new Date('2024-01-12'),
    isPaid: true,
    paidDate: new Date('2024-01-14'),
    notes: 'Paid with exact amount'
  },
  {
    customerName: 'Maria Santos',
    productName: 'Bear Brand Milk Drink 300ml',
    productPrice: 28.00,
    quantity: 1,
    dateOfDebt: new Date('2024-01-20'),
    isPaid: false,
    notes: 'For her child'
  },
  {
    customerName: 'Rosa Mendoza',
    productName: 'Safeguard Soap Classic White',
    productPrice: 18.00,
    quantity: 2,
    dateOfDebt: new Date('2024-01-16'),
    isPaid: false,
    notes: 'New customer'
  },
  {
    customerName: 'Carlos Villanueva',
    productName: 'Century Tuna Flakes in Oil 180g',
    productPrice: 38.00,
    quantity: 1,
    dateOfDebt: new Date('2024-01-08'),
    isPaid: true,
    paidDate: new Date('2024-01-11'),
    notes: 'Loyal customer'
  },
  {
    customerName: 'Luz Fernandez',
    productName: 'Tide Powder Detergent 35g',
    productPrice: 12.00,
    quantity: 3,
    dateOfDebt: new Date('2024-01-19'),
    isPaid: false,
    notes: 'Bulk purchase'
  },
  {
    customerName: 'Ana Reyes',
    productName: 'Silver Swan Soy Sauce 200ml',
    productPrice: 22.00,
    quantity: 1,
    dateOfDebt: new Date('2024-01-21'),
    isPaid: false,
    notes: 'For cooking'
  },
  {
    customerName: 'Juan Dela Cruz',
    productName: 'Skyflakes Crackers 250g',
    productPrice: 32.00,
    quantity: 1,
    dateOfDebt: new Date('2024-01-17'),
    isPaid: true,
    paidDate: new Date('2024-01-19'),
    notes: 'Quick payment'
  }
];

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Connect to database
    await connectDB();
    console.log('✅ Connected to MongoDB');

    // Clear existing data (optional - comment out if you want to keep existing data)
    console.log('🧹 Clearing existing data...');
    await Product.deleteMany({});
    await CustomerDebt.deleteMany({});
    console.log('✅ Existing data cleared');

    // Seed Products
    console.log('📦 Seeding products...');
    const createdProducts = await Product.insertMany(sampleProducts);
    console.log(`✅ Created ${createdProducts.length} products`);

    // Seed Customer Debts
    console.log('💰 Seeding customer debts...');
    const createdDebts = await CustomerDebt.insertMany(sampleDebts);
    console.log(`✅ Created ${createdDebts.length} customer debts`);

    // Display summary
    console.log('\n📊 SEEDING SUMMARY:');
    console.log(`   Products: ${createdProducts.length}`);
    console.log(`   Customer Debts: ${createdDebts.length}`);
    console.log('\n🎉 Database seeding completed successfully!');
    
    // Display some statistics
    const totalStockValue = createdProducts.reduce((sum, product) => 
      sum + (product.price * product.stockQuantity), 0
    );
    const totalDebtAmount = createdDebts.reduce((sum, debt) => 
      sum + debt.totalAmount, 0
    );
    const unpaidDebtAmount = createdDebts
      .filter(debt => !debt.isPaid)
      .reduce((sum, debt) => sum + debt.totalAmount, 0);

    console.log('\n💹 BUSINESS METRICS:');
    console.log(`   Total Stock Value: ₱${totalStockValue.toFixed(2)}`);
    console.log(`   Total Debt Amount: ₱${totalDebtAmount.toFixed(2)}`);
    console.log(`   Unpaid Debt Amount: ₱${unpaidDebtAmount.toFixed(2)}`);

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the seeder
if (require.main === module) {
  seedDatabase();
}

export default seedDatabase;
