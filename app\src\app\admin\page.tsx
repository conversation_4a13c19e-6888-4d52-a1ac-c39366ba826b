'use client';

import { useState, useEffect } from 'react';
import { Database, Loader2, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';

interface SeedStatus {
  isSeeded: boolean;
  productCount: number;
  debtCount: number;
  sampleDataAvailable: {
    products: number;
    debts: number;
  };
}

interface SeedResult {
  success: boolean;
  message: string;
  data: {
    products: { created: number; existing: number };
    debts: { created: number; existing: number };
    statistics: {
      totalProducts: number;
      totalDebts: number;
      totalStockValue: number;
      totalDebtAmount: number;
      unpaidDebtAmount: number;
    };
  };
}

export default function AdminPage() {
  const [seedStatus, setSeedStatus] = useState<SeedStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSeeding, setIsSeeding] = useState(false);
  const [seedResult, setSeedResult] = useState<SeedResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Fetch seed status on component mount
  useEffect(() => {
    fetchSeedStatus();
  }, []);

  const fetchSeedStatus = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/seed');
      const data = await response.json();
      
      if (data.success) {
        setSeedStatus(data.data);
      } else {
        setError(data.error || 'Failed to fetch seed status');
      }
    } catch (err) {
      setError('Network error while fetching seed status');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSeedDatabase = async (clearExisting = false) => {
    setIsSeeding(true);
    setError(null);
    setSeedResult(null);

    try {
      const response = await fetch('/api/admin/seed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ clearExisting }),
      });

      const data = await response.json();
      
      if (data.success) {
        setSeedResult(data);
        // Refresh seed status
        await fetchSeedStatus();
      } else {
        setError(data.error || 'Failed to seed database');
      }
    } catch (err) {
      setError('Network error while seeding database');
    } finally {
      setIsSeeding(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center space-x-3">
            <Database className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Database Administration</h1>
              <p className="text-gray-600">Manage sample data for the Sari-Sari Store system</p>
            </div>
          </div>
        </div>

        {/* Current Status */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Database Status</h2>
            <button
              onClick={fetchSeedStatus}
              disabled={isLoading}
              className="flex items-center space-x-2 px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>

          {isLoading ? (
            <div className="flex items-center space-x-2 text-gray-600">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading status...</span>
            </div>
          ) : seedStatus ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="font-medium text-blue-900 mb-2">Products</h3>
                <p className="text-2xl font-bold text-blue-600">{seedStatus.productCount}</p>
                <p className="text-sm text-blue-700">Current products in database</p>
              </div>
              <div className="bg-green-50 rounded-lg p-4">
                <h3 className="font-medium text-green-900 mb-2">Customer Debts</h3>
                <p className="text-2xl font-bold text-green-600">{seedStatus.debtCount}</p>
                <p className="text-sm text-green-700">Current debt records</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center space-x-2 text-red-600">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          ) : null}
        </div>

        {/* Seeding Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Sample Data Management</h2>
          
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={() => handleSeedDatabase(false)}
                disabled={isSeeding}
                className="flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSeeding ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <CheckCircle className="h-4 w-4" />
                )}
                <span>Add Sample Data</span>
              </button>
              
              <button
                onClick={() => handleSeedDatabase(true)}
                disabled={isSeeding}
                className="flex items-center justify-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSeeding ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <AlertCircle className="h-4 w-4" />
                )}
                <span>Reset & Seed Database</span>
              </button>
            </div>
            
            <div className="text-sm text-gray-600">
              <p><strong>Add Sample Data:</strong> Adds sample products and debts if database is empty</p>
              <p><strong>Reset & Seed:</strong> Clears all existing data and adds fresh sample data</p>
            </div>
          </div>
        </div>

        {/* Seed Result */}
        {seedResult && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Seeding Results</h2>
            
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-800">{seedResult.message}</span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="font-medium text-blue-900 mb-2">Products</h3>
                <p className="text-sm text-blue-700">
                  Created: {seedResult.data.products.created} | 
                  Existing: {seedResult.data.products.existing}
                </p>
              </div>
              <div className="bg-green-50 rounded-lg p-4">
                <h3 className="font-medium text-green-900 mb-2">Customer Debts</h3>
                <p className="text-sm text-green-700">
                  Created: {seedResult.data.debts.created} | 
                  Existing: {seedResult.data.debts.existing}
                </p>
              </div>
            </div>

            {seedResult.data.statistics && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-2">Business Statistics</h3>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Total Stock Value:</span>
                    <p className="font-semibold">₱{seedResult.data.statistics.totalStockValue.toFixed(2)}</p>
                  </div>
                  <div>
                    <span className="text-gray-600">Total Debt Amount:</span>
                    <p className="font-semibold">₱{seedResult.data.statistics.totalDebtAmount.toFixed(2)}</p>
                  </div>
                  <div>
                    <span className="text-gray-600">Unpaid Debt:</span>
                    <p className="font-semibold text-red-600">₱{seedResult.data.statistics.unpaidDebtAmount.toFixed(2)}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <span className="font-medium text-red-800">Error: {error}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
