'use client';

import { useState, useEffect, useCallback } from 'react';
import Navigation from '@/components/Navigation';
import {
  BarChart3,
  TrendingUp,
  AlertTriangle,
  Users,
  Package,
  DollarSign,
  Calendar,
  Target,
  Loader2,
  RefreshCw
} from 'lucide-react';

interface AnalyticsData {
  inventoryAnalytics: Array<{
    category: string;
    totalProducts: number;
    totalStockValue: number;
    averagePrice: number;
    totalStock: number;
    lowStockCount: number;
    outOfStockCount: number;
  }>;
  customerAnalytics: Array<{
    customerName: string;
    totalDebts: number;
    totalAmount: number;
    unpaidAmount: number;
    unpaidCount: number;
    paymentRate: number;
    averageDebtAmount: number;
  }>;
  productPerformance: Array<{
    productName: string;
    totalSold: number;
    totalRevenue: number;
    averagePrice: number;
    uniqueCustomers: number;
  }>;
  overdueDebts: Array<{
    customerName: string;
    productName: string;
    totalAmount: number;
    dateOfDebt: string;
    daysOverdue: number;
  }>;
  riskCustomers: Array<{
    customerName: string;
    unpaidAmount: number;
    riskLevel: string;
    riskFactors: string[];
  }>;
  businessSummary: {
    totalRevenue: number;
    totalUnpaidAmount: number;
    totalInventoryValue: number;
    averageDebtAmount: number;
    paymentRate: number;
  };
}

export default function AnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('30');

  const fetchAnalytics = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/analytics?period=${period}`);
      const data = await response.json();

      if (data.success) {
        setAnalytics(data.data);
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
    }
  }, [period]);

  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-orange-600 bg-orange-50 border-orange-200';
      default: return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center py-20">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="text-lg text-gray-600">Loading analytics...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Business Analytics</h1>
            <p className="text-gray-600">Advanced insights and performance metrics</p>
          </div>
          
          <div className="flex items-center space-x-4 mt-4 sm:mt-0">
            <select
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
              <option value="365">Last year</option>
            </select>
            
            <button
              onClick={fetchAnalytics}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </button>
          </div>
        </div>

        {analytics && (
          <>
            {/* Business Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <DollarSign className="h-8 w-8 text-green-600 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p className="text-2xl font-bold text-gray-900">
                      ₱{analytics.businessSummary.totalRevenue.toFixed(2)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <AlertTriangle className="h-8 w-8 text-red-600 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Unpaid Amount</p>
                    <p className="text-2xl font-bold text-gray-900">
                      ₱{analytics.businessSummary.totalUnpaidAmount.toFixed(2)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <Package className="h-8 w-8 text-blue-600 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Inventory Value</p>
                    <p className="text-2xl font-bold text-gray-900">
                      ₱{analytics.businessSummary.totalInventoryValue.toFixed(2)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <Target className="h-8 w-8 text-purple-600 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Avg Debt</p>
                    <p className="text-2xl font-bold text-gray-900">
                      ₱{analytics.businessSummary.averageDebtAmount.toFixed(2)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <TrendingUp className="h-8 w-8 text-orange-600 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Payment Rate</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {analytics.businessSummary.paymentRate.toFixed(1)}%
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              {/* Inventory Analytics */}
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center mb-4">
                  <BarChart3 className="h-6 w-6 text-blue-600 mr-2" />
                  <h2 className="text-lg font-semibold text-gray-900">Inventory by Category</h2>
                </div>
                <div className="space-y-4">
                  {analytics.inventoryAnalytics.map((category) => (
                    <div key={category.category} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium text-gray-900 capitalize">{category.category}</h3>
                        <span className="text-sm font-bold text-blue-600">
                          ₱{category.totalStockValue.toFixed(2)}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                        <div>Products: {category.totalProducts}</div>
                        <div>Stock: {category.totalStock}</div>
                        <div>Avg Price: ₱{category.averagePrice.toFixed(2)}</div>
                        <div className="text-red-600">Low Stock: {category.lowStockCount}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Top Performing Products */}
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center mb-4">
                  <TrendingUp className="h-6 w-6 text-green-600 mr-2" />
                  <h2 className="text-lg font-semibold text-gray-900">Top Products</h2>
                </div>
                <div className="space-y-3">
                  {analytics.productPerformance.slice(0, 5).map((product, index) => (
                    <div key={product.productName} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <span className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                          {index + 1}
                        </span>
                        <div>
                          <p className="font-medium text-gray-900">{product.productName}</p>
                          <p className="text-sm text-gray-600">
                            {product.totalSold} sold • {product.uniqueCustomers} customers
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-green-600">₱{product.totalRevenue.toFixed(2)}</p>
                        <p className="text-sm text-gray-500">₱{product.averagePrice.toFixed(2)} avg</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Risk Management Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Overdue Debts */}
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center mb-4">
                  <Calendar className="h-6 w-6 text-red-600 mr-2" />
                  <h2 className="text-lg font-semibold text-gray-900">Overdue Debts</h2>
                </div>
                {analytics.overdueDebts.length > 0 ? (
                  <div className="space-y-3">
                    {analytics.overdueDebts.slice(0, 5).map((debt) => (
                      <div key={`${debt.customerName}-${debt.productName}`} className="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-gray-900">{debt.customerName}</p>
                            <p className="text-sm text-gray-600">{debt.productName}</p>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-red-600">₱{debt.totalAmount.toFixed(2)}</p>
                            <p className="text-sm text-red-500">{debt.daysOverdue} days overdue</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-4">No overdue debts</p>
                )}
              </div>

              {/* Risk Customers */}
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center mb-4">
                  <Users className="h-6 w-6 text-orange-600 mr-2" />
                  <h2 className="text-lg font-semibold text-gray-900">Risk Customers</h2>
                </div>
                {analytics.riskCustomers.length > 0 ? (
                  <div className="space-y-3">
                    {analytics.riskCustomers.slice(0, 5).map((customer) => (
                      <div key={customer.customerName} className={`p-3 border rounded-lg ${getRiskColor(customer.riskLevel)}`}>
                        <div className="flex justify-between items-start mb-2">
                          <p className="font-medium">{customer.customerName}</p>
                          <div className="text-right">
                            <p className="font-bold">₱{customer.unpaidAmount.toFixed(2)}</p>
                            <span className={`text-xs px-2 py-1 rounded-full ${customer.riskLevel === 'high' ? 'bg-red-100 text-red-800' : customer.riskLevel === 'medium' ? 'bg-orange-100 text-orange-800' : 'bg-yellow-100 text-yellow-800'}`}>
                              {customer.riskLevel.toUpperCase()} RISK
                            </span>
                          </div>
                        </div>
                        <div className="text-sm">
                          {customer.riskFactors.map((factor, index) => (
                            <span key={index} className="inline-block mr-2 mb-1">• {factor}</span>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-4">No high-risk customers</p>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
