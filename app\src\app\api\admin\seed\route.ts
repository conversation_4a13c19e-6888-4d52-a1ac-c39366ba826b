import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Product from '@/lib/models/Product';
import CustomerDebt from '@/lib/models/CustomerDebt';

// Sample Products Data - Realistic Filipino Sari-Sari Store Inventory
const sampleProducts = [
  {
    name: 'Lucky Me! Pancit Canton Original',
    image: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400',
    netWeight: '60g',
    price: 15.00,
    stockQuantity: 50,
    category: 'instant foods'
  },
  {
    name: 'Coca-Cola 330ml',
    image: 'https://images.unsplash.com/photo-1554866585-cd94860890b7?w=400',
    netWeight: '330ml',
    price: 25.00,
    stockQuantity: 30,
    category: 'beverages'
  },
  {
    name: 'Argentina Corned Beef 150g',
    image: 'https://images.unsplash.com/photo-1544025162-d76694265947?w=400',
    netWeight: '150g',
    price: 45.00,
    stockQuantity: 20,
    category: 'canned goods'
  },
  {
    name: 'Chippy Barbecue 110g',
    image: 'https://images.unsplash.com/photo-1566478989037-eec170784d0b?w=400',
    netWeight: '110g',
    price: 35.00,
    stockQuantity: 25,
    category: 'snacks'
  },
  {
    name: 'Safeguard Soap Classic White',
    image: 'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400',
    netWeight: '90g',
    price: 18.00,
    stockQuantity: 15,
    category: 'personal care'
  },
  {
    name: 'Tide Powder Detergent 35g',
    image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
    netWeight: '35g',
    price: 12.00,
    stockQuantity: 40,
    category: 'household'
  },
  {
    name: 'Silver Swan Soy Sauce 200ml',
    image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400',
    netWeight: '200ml',
    price: 22.00,
    stockQuantity: 18,
    category: 'condiments'
  },
  {
    name: 'Bear Brand Milk Drink 300ml',
    image: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?w=400',
    netWeight: '300ml',
    price: 28.00,
    stockQuantity: 12,
    category: 'dairy'
  },
  {
    name: 'Skyflakes Crackers 250g',
    image: 'https://images.unsplash.com/photo-1558961363-fa8fdf82db35?w=400',
    netWeight: '250g',
    price: 32.00,
    stockQuantity: 22,
    category: 'snacks'
  },
  {
    name: 'Century Tuna Flakes in Oil 180g',
    image: 'https://images.unsplash.com/photo-1544025162-d76694265947?w=400',
    netWeight: '180g',
    price: 38.00,
    stockQuantity: 16,
    category: 'canned goods'
  }
];

// Sample Customer Debts Data - Realistic Utang Scenarios
const sampleDebts = [
  {
    customerName: 'Maria Santos',
    productName: 'Lucky Me! Pancit Canton Original',
    productPrice: 15.00,
    quantity: 3,
    dateOfDebt: new Date('2024-01-15'),
    isPaid: false,
    notes: 'Regular customer, good payment history'
  },
  {
    customerName: 'Juan Dela Cruz',
    productName: 'Coca-Cola 330ml',
    productPrice: 25.00,
    quantity: 2,
    dateOfDebt: new Date('2024-01-10'),
    isPaid: true,
    paidDate: new Date('2024-01-12'),
    notes: 'Paid on time'
  },
  {
    customerName: 'Ana Reyes',
    productName: 'Argentina Corned Beef 150g',
    productPrice: 45.00,
    quantity: 1,
    dateOfDebt: new Date('2024-01-18'),
    isPaid: false,
    notes: 'Will pay on Friday'
  },
  {
    customerName: 'Pedro Garcia',
    productName: 'Chippy Barbecue 110g',
    productPrice: 35.00,
    quantity: 2,
    dateOfDebt: new Date('2024-01-12'),
    isPaid: true,
    paidDate: new Date('2024-01-14'),
    notes: 'Paid with exact amount'
  },
  {
    customerName: 'Maria Santos',
    productName: 'Bear Brand Milk Drink 300ml',
    productPrice: 28.00,
    quantity: 1,
    dateOfDebt: new Date('2024-01-20'),
    isPaid: false,
    notes: 'For her child'
  },
  {
    customerName: 'Rosa Mendoza',
    productName: 'Safeguard Soap Classic White',
    productPrice: 18.00,
    quantity: 2,
    dateOfDebt: new Date('2024-01-16'),
    isPaid: false,
    notes: 'New customer'
  },
  {
    customerName: 'Carlos Villanueva',
    productName: 'Century Tuna Flakes in Oil 180g',
    productPrice: 38.00,
    quantity: 1,
    dateOfDebt: new Date('2024-01-08'),
    isPaid: true,
    paidDate: new Date('2024-01-11'),
    notes: 'Loyal customer'
  },
  {
    customerName: 'Luz Fernandez',
    productName: 'Tide Powder Detergent 35g',
    productPrice: 12.00,
    quantity: 3,
    dateOfDebt: new Date('2024-01-19'),
    isPaid: false,
    notes: 'Bulk purchase'
  },
  {
    customerName: 'Ana Reyes',
    productName: 'Silver Swan Soy Sauce 200ml',
    productPrice: 22.00,
    quantity: 1,
    dateOfDebt: new Date('2024-01-21'),
    isPaid: false,
    notes: 'For cooking'
  },
  {
    customerName: 'Juan Dela Cruz',
    productName: 'Skyflakes Crackers 250g',
    productPrice: 32.00,
    quantity: 1,
    dateOfDebt: new Date('2024-01-17'),
    isPaid: true,
    paidDate: new Date('2024-01-19'),
    notes: 'Quick payment'
  }
];

// POST /api/admin/seed - Seed the database with sample data
export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const { clearExisting = false } = await request.json().catch(() => ({}));

    let result = {
      success: true,
      message: 'Database seeded successfully',
      data: {
        products: { created: 0, existing: 0 },
        debts: { created: 0, existing: 0 },
        statistics: {
          totalProducts: 0,
          totalDebts: 0,
          totalStockValue: 0,
          totalDebtAmount: 0,
          unpaidDebtAmount: 0
        }
      }
    };

    // Clear existing data if requested
    if (clearExisting) {
      await Product.deleteMany({});
      await CustomerDebt.deleteMany({});
      result.message += ' (existing data cleared)';
    }

    // Seed Products
    const existingProducts = await Product.find({});
    if (existingProducts.length === 0) {
      const createdProducts = await Product.insertMany(sampleProducts);
      result.data.products.created = createdProducts.length;
    } else {
      result.data.products.existing = existingProducts.length;
    }

    // Seed Customer Debts
    const existingDebts = await CustomerDebt.find({});
    if (existingDebts.length === 0) {
      const createdDebts = await CustomerDebt.insertMany(sampleDebts);
      result.data.debts.created = createdDebts.length;
    } else {
      result.data.debts.existing = existingDebts.length;
    }

    // Calculate statistics
    const allProducts = await Product.find({});
    const allDebts = await CustomerDebt.find({});
    
    const totalStockValue = allProducts.reduce((sum, product) => 
      sum + (product.price * product.stockQuantity), 0
    );
    const totalDebtAmount = allDebts.reduce((sum, debt) => 
      sum + debt.totalAmount, 0
    );
    const unpaidDebtAmount = allDebts
      .filter(debt => !debt.isPaid)
      .reduce((sum, debt) => sum + debt.totalAmount, 0);

    result.data.statistics = {
      totalProducts: allProducts.length,
      totalDebts: allDebts.length,
      totalStockValue: parseFloat(totalStockValue.toFixed(2)),
      totalDebtAmount: parseFloat(totalDebtAmount.toFixed(2)),
      unpaidDebtAmount: parseFloat(unpaidDebtAmount.toFixed(2))
    };

    return NextResponse.json(result);

  } catch (error) {
    console.error('Error seeding database:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to seed database',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET /api/admin/seed - Get seeding status
export async function GET() {
  try {
    await connectDB();

    const productCount = await Product.countDocuments();
    const debtCount = await CustomerDebt.countDocuments();

    return NextResponse.json({
      success: true,
      data: {
        isSeeded: productCount > 0 || debtCount > 0,
        productCount,
        debtCount,
        sampleDataAvailable: {
          products: sampleProducts.length,
          debts: sampleDebts.length
        }
      }
    });

  } catch (error) {
    console.error('Error checking seed status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to check seed status'
      },
      { status: 500 }
    );
  }
}
