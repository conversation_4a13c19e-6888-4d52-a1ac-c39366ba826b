import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Product from '@/lib/models/Product';
import CustomerDebt from '@/lib/models/CustomerDebt';

// GET /api/analytics - Get advanced analytics data
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // days
    const periodDays = parseInt(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - periodDays);

    // 1. Inventory Analytics
    const inventoryAnalytics = await Product.aggregate([
      {
        $group: {
          _id: '$category',
          totalProducts: { $sum: 1 },
          totalStockValue: { $sum: { $multiply: ['$price', '$stockQuantity'] } },
          averagePrice: { $avg: '$price' },
          totalStock: { $sum: '$stockQuantity' },
          lowStockCount: {
            $sum: { $cond: [{ $lte: ['$stockQuantity', 5] }, 1, 0] }
          },
          outOfStockCount: {
            $sum: { $cond: [{ $eq: ['$stockQuantity', 0] }, 1, 0] }
          }
        }
      },
      {
        $project: {
          category: '$_id',
          totalProducts: 1,
          totalStockValue: { $round: ['$totalStockValue', 2] },
          averagePrice: { $round: ['$averagePrice', 2] },
          totalStock: 1,
          lowStockCount: 1,
          outOfStockCount: 1,
          _id: 0
        }
      },
      { $sort: { totalStockValue: -1 } }
    ]);

    // 2. Customer Debt Analytics
    const customerAnalytics = await CustomerDebt.aggregate([
      {
        $group: {
          _id: '$customerName',
          totalDebts: { $sum: 1 },
          totalAmount: { $sum: '$totalAmount' },
          unpaidAmount: {
            $sum: { $cond: [{ $eq: ['$isPaid', false] }, '$totalAmount', 0] }
          },
          unpaidCount: {
            $sum: { $cond: [{ $eq: ['$isPaid', false] }, 1, 0] }
          },
          lastDebtDate: { $max: '$dateOfDebt' },
          averageDebtAmount: { $avg: '$totalAmount' },
          paymentRate: {
            $avg: { $cond: [{ $eq: ['$isPaid', true] }, 1, 0] }
          }
        }
      },
      {
        $project: {
          customerName: '$_id',
          totalDebts: 1,
          totalAmount: { $round: ['$totalAmount', 2] },
          unpaidAmount: { $round: ['$unpaidAmount', 2] },
          unpaidCount: 1,
          lastDebtDate: 1,
          averageDebtAmount: { $round: ['$averageDebtAmount', 2] },
          paymentRate: { $round: [{ $multiply: ['$paymentRate', 100] }, 1] },
          _id: 0
        }
      },
      { $sort: { unpaidAmount: -1 } }
    ]);

    // 3. Product Performance Analytics
    const productPerformance = await CustomerDebt.aggregate([
      {
        $group: {
          _id: '$productName',
          totalSold: { $sum: '$quantity' },
          totalRevenue: { $sum: '$totalAmount' },
          averagePrice: { $avg: '$productPrice' },
          uniqueCustomers: { $addToSet: '$customerName' },
          lastSoldDate: { $max: '$dateOfDebt' }
        }
      },
      {
        $project: {
          productName: '$_id',
          totalSold: 1,
          totalRevenue: { $round: ['$totalRevenue', 2] },
          averagePrice: { $round: ['$averagePrice', 2] },
          uniqueCustomers: { $size: '$uniqueCustomers' },
          lastSoldDate: 1,
          _id: 0
        }
      },
      { $sort: { totalRevenue: -1 } },
      { $limit: 10 }
    ]);

    // 4. Overdue Debts Analysis
    const overdueThreshold = new Date();
    overdueThreshold.setDate(overdueThreshold.getDate() - 7); // 7 days overdue

    const overdueDebts = await CustomerDebt.find({
      isPaid: false,
      dateOfDebt: { $lt: overdueThreshold }
    })
    .select('customerName productName totalAmount dateOfDebt notes')
    .sort({ dateOfDebt: 1 })
    .lean();

    // Calculate days overdue for each debt
    const overdueWithDays = overdueDebts.map(debt => ({
      ...debt,
      daysOverdue: Math.floor((Date.now() - new Date(debt.dateOfDebt).getTime()) / (1000 * 60 * 60 * 24))
    }));

    // 5. Monthly Trends (last 6 months)
    const monthlyTrends = await CustomerDebt.aggregate([
      {
        $match: {
          dateOfDebt: { $gte: new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$dateOfDebt' },
            month: { $month: '$dateOfDebt' }
          },
          totalDebts: { $sum: 1 },
          totalAmount: { $sum: '$totalAmount' },
          paidDebts: {
            $sum: { $cond: [{ $eq: ['$isPaid', true] }, 1, 0] }
          },
          paidAmount: {
            $sum: { $cond: [{ $eq: ['$isPaid', true] }, '$totalAmount', 0] }
          }
        }
      },
      {
        $project: {
          month: '$_id.month',
          year: '$_id.year',
          totalDebts: 1,
          totalAmount: { $round: ['$totalAmount', 2] },
          paidDebts: 1,
          paidAmount: { $round: ['$paidAmount', 2] },
          paymentRate: {
            $round: [
              { $multiply: [{ $divide: ['$paidDebts', '$totalDebts'] }, 100] },
              1
            ]
          },
          _id: 0
        }
      },
      { $sort: { year: 1, month: 1 } }
    ]);

    // 6. Risk Assessment
    const riskCustomers = customerAnalytics
      .filter(customer => 
        customer.unpaidAmount > 100 || 
        customer.paymentRate < 50 || 
        customer.unpaidCount > 3
      )
      .map(customer => ({
        ...customer,
        riskLevel: customer.unpaidAmount > 200 ? 'high' : 
                  customer.unpaidAmount > 100 ? 'medium' : 'low',
        riskFactors: [
          ...(customer.unpaidAmount > 100 ? ['High unpaid amount'] : []),
          ...(customer.paymentRate < 50 ? ['Low payment rate'] : []),
          ...(customer.unpaidCount > 3 ? ['Multiple unpaid debts'] : [])
        ]
      }));

    // 7. Business Summary
    const businessSummary = {
      totalRevenue: customerAnalytics.reduce((sum, c) => sum + c.totalAmount, 0),
      totalUnpaidAmount: customerAnalytics.reduce((sum, c) => sum + c.unpaidAmount, 0),
      totalInventoryValue: inventoryAnalytics.reduce((sum, c) => sum + c.totalStockValue, 0),
      averageDebtAmount: customerAnalytics.length > 0 ? 
        customerAnalytics.reduce((sum, c) => sum + c.averageDebtAmount, 0) / customerAnalytics.length : 0,
      paymentRate: customerAnalytics.length > 0 ?
        customerAnalytics.reduce((sum, c) => sum + c.paymentRate, 0) / customerAnalytics.length : 0
    };

    const analytics = {
      inventoryAnalytics,
      customerAnalytics: customerAnalytics.slice(0, 10), // Top 10 customers
      productPerformance,
      overdueDebts: overdueWithDays,
      monthlyTrends,
      riskCustomers,
      businessSummary: {
        ...businessSummary,
        totalRevenue: Math.round(businessSummary.totalRevenue * 100) / 100,
        totalUnpaidAmount: Math.round(businessSummary.totalUnpaidAmount * 100) / 100,
        totalInventoryValue: Math.round(businessSummary.totalInventoryValue * 100) / 100,
        averageDebtAmount: Math.round(businessSummary.averageDebtAmount * 100) / 100,
        paymentRate: Math.round(businessSummary.paymentRate * 10) / 10
      },
      generatedAt: new Date().toISOString(),
      period: `${periodDays} days`
    };

    return NextResponse.json({
      success: true,
      data: analytics
    });

  } catch (error) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch analytics data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
