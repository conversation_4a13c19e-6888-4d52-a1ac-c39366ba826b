import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Product from '@/lib/models/Product';
import CustomerDebt from '@/lib/models/CustomerDebt';

interface Notification {
  id: string;
  type: 'low_stock' | 'out_of_stock' | 'overdue_debt' | 'high_risk_customer' | 'payment_reminder';
  title: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  data: any;
  createdAt: Date;
}

// GET /api/notifications - Get system notifications and alerts
export async function GET() {
  try {
    await connectDB();

    const notifications: Notification[] = [];

    // 1. Low Stock Alerts
    const lowStockProducts = await Product.find({
      stockQuantity: { $lte: 5, $gt: 0 }
    }).lean();

    lowStockProducts.forEach(product => {
      notifications.push({
        id: `low_stock_${product._id}`,
        type: 'low_stock',
        title: 'Low Stock Alert',
        message: `${product.name} is running low (${product.stockQuantity} left)`,
        severity: product.stockQuantity <= 2 ? 'high' : 'medium',
        data: {
          productId: product._id,
          productName: product.name,
          currentStock: product.stockQuantity,
          category: product.category,
          price: product.price
        },
        createdAt: new Date()
      });
    });

    // 2. Out of Stock Alerts
    const outOfStockProducts = await Product.find({
      stockQuantity: 0
    }).lean();

    outOfStockProducts.forEach(product => {
      notifications.push({
        id: `out_of_stock_${product._id}`,
        type: 'out_of_stock',
        title: 'Out of Stock',
        message: `${product.name} is out of stock`,
        severity: 'critical',
        data: {
          productId: product._id,
          productName: product.name,
          category: product.category,
          price: product.price
        },
        createdAt: new Date()
      });
    });

    // 3. Overdue Debt Alerts
    const overdueThreshold = new Date();
    overdueThreshold.setDate(overdueThreshold.getDate() - 7); // 7 days overdue

    const overdueDebts = await CustomerDebt.find({
      isPaid: false,
      dateOfDebt: { $lt: overdueThreshold }
    }).lean();

    // Group overdue debts by customer
    const overdueByCustomer = overdueDebts.reduce((acc, debt) => {
      if (!acc[debt.customerName]) {
        acc[debt.customerName] = {
          debts: [],
          totalAmount: 0,
          oldestDebt: debt.dateOfDebt
        };
      }
      acc[debt.customerName].debts.push(debt);
      acc[debt.customerName].totalAmount += debt.totalAmount;
      if (debt.dateOfDebt < acc[debt.customerName].oldestDebt) {
        acc[debt.customerName].oldestDebt = debt.dateOfDebt;
      }
      return acc;
    }, {} as Record<string, any>);

    Object.entries(overdueByCustomer).forEach(([customerName, data]) => {
      const daysOverdue = Math.floor((Date.now() - new Date(data.oldestDebt).getTime()) / (1000 * 60 * 60 * 24));
      
      notifications.push({
        id: `overdue_debt_${customerName.replace(/\s+/g, '_')}`,
        type: 'overdue_debt',
        title: 'Overdue Debt',
        message: `${customerName} has ₱${data.totalAmount.toFixed(2)} in overdue debts (${daysOverdue} days)`,
        severity: daysOverdue > 14 ? 'critical' : daysOverdue > 7 ? 'high' : 'medium',
        data: {
          customerName,
          totalAmount: data.totalAmount,
          debtCount: data.debts.length,
          daysOverdue,
          oldestDebt: data.oldestDebt,
          debts: data.debts
        },
        createdAt: new Date()
      });
    });

    // 4. High Risk Customer Alerts
    const customerRiskAnalysis = await CustomerDebt.aggregate([
      {
        $group: {
          _id: '$customerName',
          totalDebts: { $sum: 1 },
          totalAmount: { $sum: '$totalAmount' },
          unpaidAmount: {
            $sum: { $cond: [{ $eq: ['$isPaid', false] }, '$totalAmount', 0] }
          },
          unpaidCount: {
            $sum: { $cond: [{ $eq: ['$isPaid', false] }, 1, 0] }
          },
          paymentRate: {
            $avg: { $cond: [{ $eq: ['$isPaid', true] }, 1, 0] }
          }
        }
      },
      {
        $match: {
          $or: [
            { unpaidAmount: { $gt: 200 } },
            { paymentRate: { $lt: 0.5 } },
            { unpaidCount: { $gt: 3 } }
          ]
        }
      }
    ]);

    customerRiskAnalysis.forEach(customer => {
      const riskFactors = [];
      let severity: 'low' | 'medium' | 'high' | 'critical' = 'medium';

      if (customer.unpaidAmount > 500) {
        riskFactors.push('Very high unpaid amount');
        severity = 'critical';
      } else if (customer.unpaidAmount > 200) {
        riskFactors.push('High unpaid amount');
        severity = 'high';
      }

      if (customer.paymentRate < 0.3) {
        riskFactors.push('Very low payment rate');
        severity = 'critical';
      } else if (customer.paymentRate < 0.5) {
        riskFactors.push('Low payment rate');
        if (severity !== 'critical') severity = 'high';
      }

      if (customer.unpaidCount > 5) {
        riskFactors.push('Many unpaid debts');
        severity = 'critical';
      } else if (customer.unpaidCount > 3) {
        riskFactors.push('Multiple unpaid debts');
        if (severity !== 'critical') severity = 'high';
      }

      notifications.push({
        id: `high_risk_${customer._id.replace(/\s+/g, '_')}`,
        type: 'high_risk_customer',
        title: 'High Risk Customer',
        message: `${customer._id} is a high-risk customer (₱${customer.unpaidAmount.toFixed(2)} unpaid)`,
        severity,
        data: {
          customerName: customer._id,
          unpaidAmount: customer.unpaidAmount,
          unpaidCount: customer.unpaidCount,
          paymentRate: Math.round(customer.paymentRate * 100),
          riskFactors
        },
        createdAt: new Date()
      });
    });

    // 5. Payment Reminders (debts 3-6 days old, not yet overdue)
    const reminderStartDate = new Date();
    reminderStartDate.setDate(reminderStartDate.getDate() - 6);
    const reminderEndDate = new Date();
    reminderEndDate.setDate(reminderEndDate.getDate() - 3);

    const paymentReminders = await CustomerDebt.find({
      isPaid: false,
      dateOfDebt: { $gte: reminderStartDate, $lte: reminderEndDate }
    }).lean();

    const remindersByCustomer = paymentReminders.reduce((acc, debt) => {
      if (!acc[debt.customerName]) {
        acc[debt.customerName] = {
          debts: [],
          totalAmount: 0
        };
      }
      acc[debt.customerName].debts.push(debt);
      acc[debt.customerName].totalAmount += debt.totalAmount;
      return acc;
    }, {} as Record<string, any>);

    Object.entries(remindersByCustomer).forEach(([customerName, data]) => {
      notifications.push({
        id: `payment_reminder_${customerName.replace(/\s+/g, '_')}`,
        type: 'payment_reminder',
        title: 'Payment Reminder',
        message: `Remind ${customerName} about ₱${data.totalAmount.toFixed(2)} in pending payments`,
        severity: 'low',
        data: {
          customerName,
          totalAmount: data.totalAmount,
          debtCount: data.debts.length,
          debts: data.debts
        },
        createdAt: new Date()
      });
    });

    // Sort notifications by severity and creation date
    const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    notifications.sort((a, b) => {
      if (severityOrder[a.severity] !== severityOrder[b.severity]) {
        return severityOrder[b.severity] - severityOrder[a.severity];
      }
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    // Summary statistics
    const summary = {
      total: notifications.length,
      critical: notifications.filter(n => n.severity === 'critical').length,
      high: notifications.filter(n => n.severity === 'high').length,
      medium: notifications.filter(n => n.severity === 'medium').length,
      low: notifications.filter(n => n.severity === 'low').length,
      byType: {
        low_stock: notifications.filter(n => n.type === 'low_stock').length,
        out_of_stock: notifications.filter(n => n.type === 'out_of_stock').length,
        overdue_debt: notifications.filter(n => n.type === 'overdue_debt').length,
        high_risk_customer: notifications.filter(n => n.type === 'high_risk_customer').length,
        payment_reminder: notifications.filter(n => n.type === 'payment_reminder').length
      }
    };

    return NextResponse.json({
      success: true,
      data: {
        notifications,
        summary,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch notifications',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
