'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  Package,
  Users,
  AlertTriangle,
  TrendingUp,
  DollarSign,
  Calendar,
  BarChart3,
  Bell,
} from 'lucide-react';
import Navigation from '@/components/Navigation';

interface DashboardStats {
  products: {
    totalProducts: number;
    lowStockProducts: number;
    totalStockValue: number;
  };
  debts: {
    totalCustomers: number;
    totalDebts: number;
    totalUnpaidDebts: number;
    totalDebtAmount: number;
    totalUnpaidAmount: number;
  };
  recentDebts: Array<{
    _id: string;
    customerName: string;
    productName: string;
    totalAmount: number;
    dateOfDebt: string;
    isPaid: boolean;
  }>;
  lowStockProducts: Array<{
    _id: string;
    name: string;
    stockQuantity: number;
    price: number;
    category: string;
  }>;
  topCustomers: Array<{
    customerName: string;
    totalUnpaid: number;
    debtCount: number;
  }>;
}

export default function Home() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      const response = await fetch('/api/dashboard/stats');
      const data = await response.json();

      if (data.success) {
        setStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <div className='min-h-screen bg-gray-50'>
        <Navigation />
        <div className='flex items-center justify-center py-20'>
          <div className='h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600'></div>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      <Navigation />

      <div className='container mx-auto px-4 py-8'>
        <div className='mb-8'>
          <h1 className='mb-2 text-3xl font-bold text-gray-900'>
            Dashboard Overview
          </h1>
          <p className='text-gray-600'>
            Monitor your sari-sari store performance and key metrics
          </p>
        </div>

        {/* Key Metrics */}
        <div className='mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4'>
          <div className='rounded-lg bg-white p-6 shadow-sm'>
            <div className='flex items-center'>
              <Package className='mr-3 h-8 w-8 text-blue-600' />
              <div>
                <p className='text-sm font-medium text-gray-600'>
                  Total Products
                </p>
                <p className='text-2xl font-bold text-gray-900'>
                  {stats?.products.totalProducts || 0}
                </p>
              </div>
            </div>
          </div>

          <div className='rounded-lg bg-white p-6 shadow-sm'>
            <div className='flex items-center'>
              <Users className='mr-3 h-8 w-8 text-green-600' />
              <div>
                <p className='text-sm font-medium text-gray-600'>
                  Total Customers
                </p>
                <p className='text-2xl font-bold text-gray-900'>
                  {stats?.debts.totalCustomers || 0}
                </p>
              </div>
            </div>
          </div>

          <div className='rounded-lg bg-white p-6 shadow-sm'>
            <div className='flex items-center'>
              <AlertTriangle className='mr-3 h-8 w-8 text-red-600' />
              <div>
                <p className='text-sm font-medium text-gray-600'>
                  Unpaid Debts
                </p>
                <p className='text-2xl font-bold text-gray-900'>
                  {stats?.debts.totalUnpaidDebts || 0}
                </p>
              </div>
            </div>
          </div>

          <div className='rounded-lg bg-white p-6 shadow-sm'>
            <div className='flex items-center'>
              <DollarSign className='mr-3 h-8 w-8 text-orange-600' />
              <div>
                <p className='text-sm font-medium text-gray-600'>
                  Unpaid Amount
                </p>
                <p className='text-2xl font-bold text-gray-900'>
                  ₱{stats?.debts.totalUnpaidAmount.toFixed(2) || '0.00'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className='grid grid-cols-1 gap-8 lg:grid-cols-3'>
          {/* Quick Actions */}
          <div className='space-y-6 lg:col-span-2'>
            <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
              <Link href='/products' className='block'>
                <div className='rounded-lg border-l-4 border-blue-500 bg-white p-6 shadow-sm transition-shadow duration-300 hover:shadow-md'>
                  <div className='mb-4 flex items-center'>
                    <Package className='mr-3 h-8 w-8 text-blue-600' />
                    <h2 className='text-xl font-bold text-gray-900'>
                      Product Management
                    </h2>
                  </div>
                  <p className='mb-4 text-gray-600'>
                    Manage inventory, track stock levels, and update product
                    information.
                  </p>
                  <div className='font-semibold text-blue-600'>
                    Manage Products →
                  </div>
                </div>
              </Link>

              <Link href='/debts' className='block'>
                <div className='rounded-lg border-l-4 border-green-500 bg-white p-6 shadow-sm transition-shadow duration-300 hover:shadow-md'>
                  <div className='mb-4 flex items-center'>
                    <Users className='mr-3 h-8 w-8 text-green-600' />
                    <h2 className='text-xl font-bold text-gray-900'>
                      Customer Debts
                    </h2>
                  </div>
                  <p className='mb-4 text-gray-600'>
                    Track customer debts (utang) and manage payment records.
                  </p>
                  <div className='font-semibold text-green-600'>
                    Manage Debts →
                  </div>
                </div>
              </Link>

              <Link href='/analytics' className='block'>
                <div className='rounded-lg border-l-4 border-purple-500 bg-white p-6 shadow-sm transition-shadow duration-300 hover:shadow-md'>
                  <div className='mb-4 flex items-center'>
                    <BarChart3 className='mr-3 h-8 w-8 text-purple-600' />
                    <h2 className='text-xl font-bold text-gray-900'>
                      Business Analytics
                    </h2>
                  </div>
                  <p className='mb-4 text-gray-600'>
                    Advanced insights, trends, and performance analytics.
                  </p>
                  <div className='font-semibold text-purple-600'>
                    View Analytics →
                  </div>
                </div>
              </Link>
            </div>

            {/* Advanced Features Section */}
            <div className='rounded-lg bg-white p-6 shadow-sm'>
              <div className='mb-4 flex items-center'>
                <Bell className='mr-2 h-6 w-6 text-orange-600' />
                <h3 className='text-lg font-semibold text-gray-900'>
                  Advanced Features
                </h3>
              </div>
              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <Link href='/notifications' className='flex items-center justify-between rounded-lg bg-orange-50 p-4 hover:bg-orange-100 transition-colors'>
                  <div>
                    <p className='font-medium text-gray-900'>Smart Notifications</p>
                    <p className='text-sm text-gray-600'>Alerts for low stock, overdue debts, and risk management</p>
                  </div>
                  <Bell className='h-5 w-5 text-orange-600' />
                </Link>
                <Link href='/admin' className='flex items-center justify-between rounded-lg bg-blue-50 p-4 hover:bg-blue-100 transition-colors'>
                  <div>
                    <p className='font-medium text-gray-900'>Database Management</p>
                    <p className='text-sm text-gray-600'>Seed sample data and manage database</p>
                  </div>
                  <Package className='h-5 w-5 text-blue-600' />
                </Link>
              </div>
            </div>

            {/* Low Stock Alert */}
            {stats?.lowStockProducts && stats.lowStockProducts.length > 0 && (
              <div className='rounded-lg bg-white p-6 shadow-sm'>
                <div className='mb-4 flex items-center'>
                  <AlertTriangle className='mr-2 h-6 w-6 text-red-600' />
                  <h3 className='text-lg font-semibold text-gray-900'>
                    Low Stock Alert
                  </h3>
                </div>
                <div className='space-y-3'>
                  {stats.lowStockProducts.slice(0, 5).map(product => (
                    <div
                      key={product._id}
                      className='flex items-center justify-between rounded-lg bg-red-50 p-3'
                    >
                      <div>
                        <p className='font-medium text-gray-900'>
                          {product.name}
                        </p>
                        <p className='text-sm capitalize text-gray-600'>
                          {product.category}
                        </p>
                      </div>
                      <div className='text-right'>
                        <p className='text-sm font-medium text-red-600'>
                          {product.stockQuantity} left
                        </p>
                        <p className='text-xs text-gray-500'>
                          ₱{product.price.toFixed(2)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
                <Link
                  href='/products'
                  className='mt-4 inline-block font-medium text-blue-600 hover:text-blue-800'
                >
                  View all products →
                </Link>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className='space-y-6'>
            {/* Recent Debts */}
            <div className='rounded-lg bg-white p-6 shadow-sm'>
              <div className='mb-4 flex items-center'>
                <Calendar className='mr-2 h-6 w-6 text-blue-600' />
                <h3 className='text-lg font-semibold text-gray-900'>
                  Recent Debts
                </h3>
              </div>
              {stats?.recentDebts && stats.recentDebts.length > 0 ? (
                <div className='space-y-3'>
                  {stats.recentDebts.slice(0, 5).map(debt => (
                    <div
                      key={debt._id}
                      className='flex items-start justify-between rounded-lg bg-gray-50 p-3'
                    >
                      <div className='flex-1'>
                        <p className='text-sm font-medium text-gray-900'>
                          {debt.customerName}
                        </p>
                        <p className='text-xs text-gray-600'>
                          {debt.productName}
                        </p>
                        <p className='text-xs text-gray-500'>
                          {formatDate(debt.dateOfDebt)}
                        </p>
                      </div>
                      <div className='text-right'>
                        <p className='text-sm font-medium text-gray-900'>
                          ₱{debt.totalAmount.toFixed(2)}
                        </p>
                        <span
                          className={`rounded-full px-2 py-1 text-xs ${
                            debt.isPaid
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {debt.isPaid ? 'Paid' : 'Unpaid'}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className='text-sm text-gray-500'>No recent debt records</p>
              )}
              <Link
                href='/debts'
                className='mt-4 inline-block font-medium text-blue-600 hover:text-blue-800'
              >
                View all debts →
              </Link>
            </div>

            {/* Top Customers by Debt */}
            {stats?.topCustomers && stats.topCustomers.length > 0 && (
              <div className='rounded-lg bg-white p-6 shadow-sm'>
                <div className='mb-4 flex items-center'>
                  <TrendingUp className='mr-2 h-6 w-6 text-orange-600' />
                  <h3 className='text-lg font-semibold text-gray-900'>
                    Top Debtors
                  </h3>
                </div>
                <div className='space-y-3'>
                  {stats.topCustomers.slice(0, 5).map(customer => (
                    <div
                      key={customer.customerName}
                      className='flex items-center justify-between rounded-lg bg-orange-50 p-3'
                    >
                      <div>
                        <p className='font-medium text-gray-900'>
                          {customer.customerName}
                        </p>
                        <p className='text-sm text-gray-600'>
                          {customer.debtCount} debt
                          {customer.debtCount > 1 ? 's' : ''}
                        </p>
                      </div>
                      <p className='text-sm font-bold text-orange-600'>
                        ₱{customer.totalUnpaid.toFixed(2)}
                      </p>
                    </div>
                  ))}
                </div>
                <Link
                  href='/debts'
                  className='mt-4 inline-block font-medium text-blue-600 hover:text-blue-800'
                >
                  View debt summary →
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
