'use client';

import { useState, useEffect } from 'react';
import {
  Bell,
  Package,
  Users,
  Calendar,
  X,
  ChevronDown,
  ChevronUp,
  ExternalLink
} from 'lucide-react';
import Link from 'next/link';

interface Notification {
  id: string;
  type: 'low_stock' | 'out_of_stock' | 'overdue_debt' | 'high_risk_customer' | 'payment_reminder';
  title: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  data: any;
  createdAt: string;
}

interface NotificationData {
  notifications: Notification[];
  summary: {
    total: number;
    critical: number;
    high: number;
    medium: number;
    low: number;
    byType: Record<string, number>;
  };
}

export default function NotificationCenter() {
  const [notifications, setNotifications] = useState<NotificationData | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [expandedNotifications, setExpandedNotifications] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchNotifications();
    // Refresh notifications every 5 minutes
    const interval = setInterval(fetchNotifications, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/notifications');
      const data = await response.json();
      
      if (data.success) {
        setNotifications(data.data);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'low_stock':
      case 'out_of_stock':
        return <Package className="h-5 w-5" />;
      case 'overdue_debt':
      case 'payment_reminder':
        return <Calendar className="h-5 w-5" />;
      case 'high_risk_customer':
        return <Users className="h-5 w-5" />;
      default:
        return <Bell className="h-5 w-5" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getActionLink = (notification: Notification) => {
    switch (notification.type) {
      case 'low_stock':
      case 'out_of_stock':
        return '/products';
      case 'overdue_debt':
      case 'payment_reminder':
      case 'high_risk_customer':
        return '/debts';
      default:
        return null;
    }
  };

  const toggleExpanded = (notificationId: string) => {
    const newExpanded = new Set(expandedNotifications);
    if (newExpanded.has(notificationId)) {
      newExpanded.delete(notificationId);
    } else {
      newExpanded.add(notificationId);
    }
    setExpandedNotifications(newExpanded);
  };

  const criticalCount = notifications?.summary.critical || 0;
  const highCount = notifications?.summary.high || 0;
  const urgentCount = criticalCount + highCount;

  return (
    <div className="relative">
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg"
      >
        <Bell className="h-6 w-6" />
        {urgentCount > 0 && (
          <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
            {urgentCount > 9 ? '9+' : urgentCount}
          </span>
        )}
      </button>

      {/* Notification Dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden">
          {/* Header */}
          <div className="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
              {notifications && (
                <p className="text-sm text-gray-600">
                  {notifications.summary.total} total • {urgentCount} urgent
                </p>
              )}
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Content */}
          <div className="max-h-80 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-sm text-gray-600 mt-2">Loading notifications...</p>
              </div>
            ) : notifications && notifications.notifications.length > 0 ? (
              <div className="divide-y divide-gray-200">
                {notifications.notifications.slice(0, 10).map((notification) => {
                  const isExpanded = expandedNotifications.has(notification.id);
                  const actionLink = getActionLink(notification);
                  
                  return (
                    <div key={notification.id} className={`p-4 ${getSeverityColor(notification.severity)}`}>
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium truncate">
                              {notification.title}
                            </p>
                            <div className="flex items-center space-x-1">
                              {actionLink && (
                                <Link
                                  href={actionLink}
                                  className="text-xs hover:underline flex items-center"
                                  onClick={() => setIsOpen(false)}
                                >
                                  <ExternalLink className="h-3 w-3" />
                                </Link>
                              )}
                              <button
                                onClick={() => toggleExpanded(notification.id)}
                                className="text-xs hover:underline"
                              >
                                {isExpanded ? (
                                  <ChevronUp className="h-3 w-3" />
                                ) : (
                                  <ChevronDown className="h-3 w-3" />
                                )}
                              </button>
                            </div>
                          </div>
                          <p className="text-sm mt-1">
                            {notification.message}
                          </p>
                          
                          {isExpanded && notification.data && (
                            <div className="mt-2 text-xs space-y-1">
                              {notification.type === 'low_stock' && (
                                <div>
                                  <p>Category: {notification.data.category}</p>
                                  <p>Price: ₱{notification.data.price.toFixed(2)}</p>
                                </div>
                              )}
                              {notification.type === 'overdue_debt' && (
                                <div>
                                  <p>Debt Count: {notification.data.debtCount}</p>
                                  <p>Days Overdue: {notification.data.daysOverdue}</p>
                                </div>
                              )}
                              {notification.type === 'high_risk_customer' && (
                                <div>
                                  <p>Payment Rate: {notification.data.paymentRate}%</p>
                                  <p>Risk Factors: {notification.data.riskFactors.join(', ')}</p>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="p-4 text-center">
                <Bell className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">No notifications</p>
              </div>
            )}
          </div>

          {/* Footer */}
          {notifications && notifications.notifications.length > 10 && (
            <div className="px-4 py-3 border-t border-gray-200 text-center">
              <Link
                href="/notifications"
                className="text-sm text-blue-600 hover:text-blue-800"
                onClick={() => setIsOpen(false)}
              >
                View all {notifications.summary.total} notifications
              </Link>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
