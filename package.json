{"name": "tindahan-root", "version": "1.0.0", "description": "Root package for Sari-Sari Store Management System", "private": true, "scripts": {"dev": "cd app && npm run dev", "build": "cd app && npm run build", "start": "cd app && npm start", "lint": "cd app && npm run lint", "install-deps": "cd app && npm install", "clean": "cd app && npm run clean", "type-check": "cd app && npm run type-check", "format": "cd app && npm run format", "test": "cd app && npm test"}, "workspaces": ["app"], "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "devDependencies": {"typescript": "^5.8.3"}}